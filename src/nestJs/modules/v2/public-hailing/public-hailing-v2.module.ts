import { <PERSON>du<PERSON> } from "@nestjs/common";

import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { HailingApiModule } from "@nest/modules/hailingApi/hailingApi.module";

import { PublicHailingV2Controller } from "./public-hailing-v2.controller";
import { PublicHailingV2Service } from "./services/public-hailing-v2.service";

@Module({
  imports: [HailingApiModule],
  providers: [PublicHailingV2Service, TxRepository, PaymentTxRepository, UserRepository],
  exports: [PublicHailingV2Service],
  controllers: [PublicHailingV2Controller],
})
export class PublicHailingV2Module {}
