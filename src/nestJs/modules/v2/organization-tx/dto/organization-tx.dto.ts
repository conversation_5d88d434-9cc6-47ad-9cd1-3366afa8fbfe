import { ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import Tx from "@nest/modules/database/entities/tx.entity";

export class OrganizationTxQueryDto {
  @ApiPropertyOptional({ 
    description: "Transaction type filter",
    enum: [TxTypes.TRIP, TxTypes.HAILING_REQUEST]
  })
  type?: TxTypes.TRIP | TxTypes.HAILING_REQUEST;

  @ApiPropertyOptional({ 
    description: "Page number for pagination",
    default: 1
  })
  page?: number;

  @ApiPropertyOptional({ 
    description: "Number of items per page",
    default: 50,
    maximum: 200
  })
  limit?: number;
}

export const organizationTxQuerySchema = Joi.object<OrganizationTxQueryDto>({
  type: Joi.string()
    .valid(TxTypes.TRIP, TxTypes.HAILING_REQUEST)
    .optional(),
  page: Joi.number()
    .integer()
    .min(1)
    .optional()
    .default(1),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(200)
    .optional()
    .default(50),
});

export interface OrganizationTxResponseDto {
  items: Tx[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
