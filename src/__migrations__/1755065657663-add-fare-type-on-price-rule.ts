import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFareTypeOnPriceRule1755065657663 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "pricing_rule" ADD "fareType" character varying NOT NULL DEFAULT \'FARE\'');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "pricing_rule" DROP COLUMN "fareType"');
    }

}
