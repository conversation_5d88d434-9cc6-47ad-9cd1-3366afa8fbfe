import { Injectable } from "@nestjs/common";
import { Filter } from "firebase-admin/firestore";

import OrganizationEntity from "@nest/modules/database/entities/organization.entity";
import { OrganizationRepository } from "@nest/modules/database/repositories/organization.repository";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { ListingResponseDto } from "@nest/modules/validation/dto/listingResponseSchema.dto";
import { DirectionType } from "@nest/modules/validation/dto/listingSchema.dto";
import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { B2bUserDocument } from "@nest/modules/appDatabase/documents/b2bUser.document";
import { OrganizationServiceRepository } from "@nest/modules/database/repositories/organizationService.repository";

import { CreateOrganizationDto } from "./dto/create-organization.dto";
import { OrganizationListingQueryDto } from "./dto/organization-listing-query.dto";

@Injectable()
export class AdminOrganizationService {
  constructor(
    private readonly organizationRepository: OrganizationRepository,
    private readonly organizationServiceRepository: OrganizationServiceRepository,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  async createOrganization(createOrganizationDto: CreateOrganizationDto): Promise<OrganizationEntity> {
    const existingOrganization = await this.organizationRepository.findOne({
      where: { name: createOrganizationDto.name },
    });
    if (existingOrganization) {
      return existingOrganization;
    }
    const organization = this.organizationRepository.create(createOrganizationDto);

    await this.organizationServiceRepository.save({
      category: createOrganizationDto.category,
      organizationId: organization.id,
    });

    return await this.organizationRepository.save(organization);
  }

  async getOrganizations(query: OrganizationListingQueryDto): Promise<ListingResponseDto<OrganizationEntity>> {
    const { limit = 20, offset = 0, search, sortDirection = DirectionType.DESC, sortBy = "createdAt" } = query;

    const queryBuilder = this.organizationRepository
      .createQueryBuilder("organization")
      .leftJoinAndSelect("organization.services", "services");

    if (search) {
      queryBuilder.where("organization.name ILIKE :search", { search: `%${search}%` });
    }

    queryBuilder.orderBy(`organization.${sortBy}`, sortDirection).skip(offset).take(limit);

    const [data, count] = await queryBuilder.getManyAndCount();

    return {
      data,
      count,
    };
  }

  async getOrganizationById(id: string): Promise<{ organization: OrganizationEntity; b2bUsers: B2bUserDocument[] }> {
    const organization = await this.organizationRepository.findOne({
      where: { id },
      relations: ["users", "services"],
    });

    const filters: Filter[] = [];
    filters.push(Filter.where("organization_id", "==", id));

    const b2bUsers = await this.appDatabaseService.b2bUserRepository().find(...filters);

    if (!organization) {
      throw errorBuilder.organization.notFound(id);
    }

    return { organization, b2bUsers };
  }
}
