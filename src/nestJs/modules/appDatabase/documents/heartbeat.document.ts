import { Timestamp } from "firebase-admin/firestore";
export class Location {
  _latitude: number;
  _longitude: number;
}
export class HeartbeatDocument {
  static readonly collectionName = "heartbeat";

  id: string;
  location?: Location;
  bearing?: number;
  device_time?: Timestamp;
  gps_type?: string;
  server_time?: Timestamp;
  speed?: number;
  meter_software_version?: string;
  acc_status?: string;

  //meter_id does not exist in the document, but is used for processing, it is generated from the document path
  meter_id: string;
}

export interface HeartbeatRow {
  id: string;
  meter_id: string;
  location: string | null;
  bearing: number | null;
  device_time: number | null;
  gps_type: string | null;
  server_time: number | null;
  speed: number | null;
  meter_software_version: string | null;
  acc_status: string | null;

  [key: string]: any;
}
