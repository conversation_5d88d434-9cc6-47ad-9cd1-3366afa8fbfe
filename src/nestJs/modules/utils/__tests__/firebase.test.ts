import { GeoPoint, Timestamp } from "firebase-admin/firestore";
import { serializeFirestoreData } from "../firebase";

describe("serializeFirestoreData", () => {
  it("should handle null and undefined", () => {
    expect(serializeFirestoreData(null)).toBe(null);
    expect(serializeFirestoreData(undefined)).toBe(null);
  });

  it("should handle primitive types", () => {
    expect(serializeFirestoreData("test")).toBe("test");
    expect(serializeFirestoreData(123)).toBe(123);
    expect(serializeFirestoreData(true)).toBe(true);
  });

  it("should serialize Firestore Timestamp", () => {
    const timestamp = Timestamp.fromDate(new Date("2023-01-01T00:00:00Z"));
    const result = serializeFirestoreData(timestamp);
    expect(result).toBe("2023-01-01T00:00:00.000Z");
  });

  it("should serialize GeoPoint", () => {
    const geoPoint = new GeoPoint(37.7749, -122.4194);
    const result = serializeFirestoreData(geoPoint);
    expect(result).toEqual({ latitude: 37.7749, longitude: -122.4194 });
  });

  it("should handle arrays", () => {
    const array = ["test", 123, true];
    const result = serializeFirestoreData(array);
    expect(result).toEqual(["test", 123, true]);
  });

  it("should handle nested objects", () => {
    const obj = {
      name: "test",
      count: 123,
      active: true,
      nested: {
        value: "nested"
      }
    };
    const result = serializeFirestoreData(obj);
    expect(result).toEqual(obj);
  });

  it("should handle objects with toJSON method (like Firestore event data)", () => {
    const firestoreEventData = {
      toJSON: jest.fn(() => ({
        id: "test123",
        name: "Test Document",
        timestamp: Timestamp.fromDate(new Date("2023-01-01T00:00:00Z")),
        location: new GeoPoint(37.7749, -122.4194),
        active: true
      }))
    };

    const result = serializeFirestoreData(firestoreEventData);
    
    expect(firestoreEventData.toJSON).toHaveBeenCalled();
    expect(result).toEqual({
      id: "test123",
      name: "Test Document",
      timestamp: "2023-01-01T00:00:00.000Z",
      location: { latitude: 37.7749, longitude: -122.4194 },
      active: true
    });
  });

  it("should handle complex nested structures with Firestore types", () => {
    const complexData = {
      user: {
        name: "John Doe",
        createdAt: Timestamp.fromDate(new Date("2023-01-01T00:00:00Z")),
        location: new GeoPoint(37.7749, -122.4194)
      },
      trips: [
        {
          id: "trip1",
          startTime: Timestamp.fromDate(new Date("2023-01-02T10:00:00Z")),
          coordinates: [
            new GeoPoint(37.7749, -122.4194),
            new GeoPoint(37.7849, -122.4094)
          ]
        }
      ]
    };

    const result = serializeFirestoreData(complexData);
    
    expect(result).toEqual({
      user: {
        name: "John Doe",
        createdAt: "2023-01-01T00:00:00.000Z",
        location: { latitude: 37.7749, longitude: -122.4194 }
      },
      trips: [
        {
          id: "trip1",
          startTime: "2023-01-02T10:00:00.000Z",
          coordinates: [
            { latitude: 37.7749, longitude: -122.4194 },
            { latitude: 37.7849, longitude: -122.4094 }
          ]
        }
      ]
    });
  });
});
