import { Modu<PERSON> } from "@nestjs/common";

import { OrganizationRepository } from "@nest/modules/database/repositories/organization.repository";

import { AdminOrganizationController } from "./admin-organization.controller";
import { AdminOrganizationService } from "./admin-organization.service";

@Module({
  imports: [],
  controllers: [AdminOrganizationController],
  providers: [AdminOrganizationService, OrganizationRepository],
  exports: [AdminOrganizationService],
})
export class AdminOrganizationModule {}