import { Inject } from "@nestjs/common";
import { ClsService } from "nestjs-cls";
import { DataSource, EntityManager } from "typeorm";
import { InjectEntityManager } from "@nestjs/typeorm";

import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

interface DecoratedClass {
  clsService?: ClsService;
  clsContextStorageService?: ClsContextStorageService;
  entityManager?: EntityManager;
  logger?: LoggerServiceAdapter;
}

export function UseMasterReadClient(): MethodDecorator {
  return (target: object, propertyKey: string | symbol, descriptor: PropertyDescriptor): PropertyDescriptor => {
    // Apply the dependency injections to the class
    Inject(ClsService)(target, "clsService");
    Inject(ClsContextStorageService)(target, "clsContextStorageService");
    Inject(LoggerServiceAdapter)(target, "logger");
    InjectEntityManager()(target, "entityManager");

    // Store the original method
    const originalMethod = descriptor.value;

    // Wrap the original method with master read client logic
    descriptor.value = async function (...args: unknown[]) {
      // Get the injected services from the class instance
      const instance = this as DecoratedClass;
      const clsService = instance.clsService;
      const clsContextStorageService = instance.clsContextStorageService;
      const entityManager = instance.entityManager;

      if (!clsService || !clsContextStorageService || !entityManager) {
        // Fallback to original method if dependencies are not available
        return await originalMethod.apply(this, args);
      }

      const dataSource: DataSource = entityManager.connection;

      return clsService.run(async () => {
        const isReplicationEnabled =
          dataSource.options.type === "postgres" && dataSource.options.replication !== undefined;
        let queryRunner;

        if (isReplicationEnabled) {
          // Use master QueryRunner for replication
          queryRunner = dataSource.createQueryRunner("master");
          await queryRunner.connect();
          instance.logger?.info("useMasterReadClient/isReplicationEnabled-end", { isReplicationEnabled });
          clsContextStorageService.setMasterEntityManager(queryRunner.manager);
        }

        try {
          // Execute the original method
          return await originalMethod.apply(this, args);
        } finally {
          if (queryRunner) {
            await queryRunner.release(); // Release QueryRunner if created
          }
        }
      });
    };

    // Preserve any existing metadata and properties from the original descriptor
    Object.defineProperty(descriptor.value, "name", {
      value: originalMethod.name,
      configurable: true,
    });

    return descriptor;
  };
}
