import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON>any, PrimaryGeneratedColumn, Relation } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import User from "./user.entity";
import Tx from "./tx.entity";
import OrganizationServiceEntity, { ServiceCategory } from "./organizationService.entity";

@Entity({ name: "organization" })
export default class OrganizationEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  name: string;

  @Column()
  category: ServiceCategory;

  @OneToMany(() => User, (user) => user.organization)
  users: Relation<User[]>;
  
  @OneToMany(() => Tx, (tx) => tx.organization)
  txs: Relation<Tx[]>;

  @OneToMany(() => OrganizationServiceEntity, (organizationService) => organizationService.organization)
  services: Relation<OrganizationServiceEntity[]>;
}
