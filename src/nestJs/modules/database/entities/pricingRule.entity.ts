import { Entity, PrimaryGeneratedColumn, Column, Index } from "typeorm";

import { ClientType, PlatformType } from "@nest/modules/v2/hailing/dto/create-hail.dto";
import { HailType } from "@nest/modules/transaction/dto/txHailingRequest.dto";

import { DefaultEntity } from "./defaultEntity";

export enum FeeType {
  FARE_ADJUSTMENT = "FARE_ADJUSTMENT",
  ADDITIONAL_BOOKING_FEE = "ADDITIONAL_BOOKING_FEE",
  DASH_BOOKING_FEE = "DASH_BOOKING_FEE",
  DASH_TRANSACTION_FEE = "DASH_TRANSACTION_FEE",
}

@Entity({ name: "pricing_rule" })
@Index("idx_pricing_rule_types", ["platformType", "clientType", "orderType"])
export default class PricingRuleEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Index("idx_pricing_rule_platform_type")
  @Column({ type: "varchar" })
  platformType: PlatformType;

  @Index("idx_pricing_rule_client_type")
  @Column({ type: "varchar" })
  clientType: ClientType;

  @Index("idx_pricing_rule_order_type")
  @Column({ type: "varchar" })
  orderType: HailType;

  @Column({ type: "varchar", nullable: false, enum: FeeType, default: FeeType.FARE_ADJUSTMENT })
  feeType: FeeType;
  
  @Column({ type: "text" })
  jsonRule: string;

  @Column({ type: "timestamp" })
  startAt: Date;

  @Column({ type: "timestamp" })
  endAt: Date;
}
