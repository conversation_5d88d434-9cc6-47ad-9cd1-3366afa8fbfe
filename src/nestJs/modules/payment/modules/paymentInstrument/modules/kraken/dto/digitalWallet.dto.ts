import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { CardType } from "./card.dto";

export enum DigitalWalletType {
  GOOGLE_PAY = "GOOGLE_PAY",
  APPLE_PAY = "APPLE_PAY",
  SAMSUNG_PAY = "SAMSUNG_PAY",
}

export class PaymentDigitalWalletBody {
  @ApiProperty({ example: "YmZlNTMyZTMtMjdkNi00Y2ZlLWI5YTAtZDlhNzYzZDI2YmQ1" })
  cardToken: string;

  @ApiProperty({ enum: DigitalWalletType, description: "Type of the payment token must be set to GOOGLE_PAY" })
  digitalWalletType: DigitalWalletType;

  @ApiProperty({ example: "1234", description: "Last 4 digits of the card" })
  cardSuffix: string;

  @ApiProperty({ enum: CardType, description: "Card network type" })
  cardNetwork: CardType;
}

export const paymentDigitalWalletBodySchema = Joi.object<PaymentDigitalWalletBody>({
  cardToken: Joi.string().required(),
  digitalWalletType: Joi.string()
    .valid(...Object.values(DigitalWalletType))
    .required(),
  cardSuffix: Joi.string().required(),
  cardNetwork: Joi.string()
    .uppercase()
    .valid(...Object.values(CardType))
    .required(),
});

export class KrakenTokenizedCardRequestDto {
  externalId?: string;
  token: string;
  cardInfo: string;
  cardNetwork: CardType;
  userId: string;
  tokenType: DigitalWalletType;
}

export const krakenTokenizedCardRequestSchema = Joi.object<KrakenTokenizedCardRequestDto>({
  externalId: Joi.string().optional(),
  token: Joi.string().required(),
  cardInfo: Joi.string().required(),
  cardNetwork: Joi.string<CardType>()
    .valid(...Object.values(CardType))
    .required(),
  userId: Joi.string().required(),
  tokenType: Joi.string<DigitalWalletType>()
    .valid(...Object.values(DigitalWalletType))
    .required(),
});
