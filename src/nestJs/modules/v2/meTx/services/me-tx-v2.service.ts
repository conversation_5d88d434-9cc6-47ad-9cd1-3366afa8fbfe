import { Injectable } from "@nestjs/common";

import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { LogAll } from "@nest/decorators/log-all.decorator";

import { GetTransactionsResponse, HailingOrder } from "../dto/me-tx.dto";

@LogAll()
@Injectable()
export class MeTxV2Service {
  constructor(private txRepository: TxRepository, private userRepository: UserRepository) {}

  async getMeTxs(userId: string, limit: number): Promise<GetTransactionsResponse> {
    const user = await this.userRepository.findAppUserById(userId);
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    const { items: transactions, total } = await this.txRepository.findTxByStatusAndUserId(user.id, limit);

    return {
      items: transactions.map((tx) => new HailingOrder(tx).toJSON()),
      total,
      limit,
    };
  }

  async getMeTxHistories(userId: string, page: number, limit: number): Promise<GetTransactionsResponse> {
    const user = await this.userRepository.findAppUserById(userId);
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    const { items: transactions, total } = await this.txRepository.findHistoricHailingTxByStatusAndUserId(
      user.id,
      page,
      limit,
    );

    return {
      items: transactions.map((tx) => new HailingOrder(tx).toJSON()),
      total,
      limit,
    };
  }
}
