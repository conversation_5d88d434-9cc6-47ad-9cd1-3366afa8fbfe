## Purpose

The keys here are backend-function's public keys created using the [EC (Elliptic Curve) algorithm](https://en.wikipedia.org/wiki/Elliptic-curve_cryptography), for reference.

The file naming format is: `[env]-public.pem`

- Other clients can use this public key to encrypt any payload, and only backend-function can decrypt it. 
- The private key is stored in secrets manager, `backend-functions-payment-private-key`.
- Other clients can also use this public key to verify the signature is valid because only the one who keeps the private key can create the signature.
- Backend-functions don't need to keep this in the app, unless needed for some reason.
