import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import { BankService } from "../../../nestJs/modules/bank/bank.service";
import { MerchantRepository } from "../../../nestJs/modules/database/repositories/merchant.repository";
import { PaymentInstrumentRepository } from "../../../nestJs/modules/database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../nestJs/modules/database/repositories/paymentTx.repository";
import { PayoutRepository } from "../../../nestJs/modules/database/repositories/payout.repository";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { TxTagRepository } from "../../../nestJs/modules/database/repositories/txTag.repository";
import { UserRepository } from "../../../nestJs/modules/database/repositories/user.repository";
import { DiscountRepository } from "../../../nestJs/modules/database/repositories/discount.repository";
import { TxEventRepository } from "../../../nestJs/modules/database/repositories/txEvent.repository";
import { PaymentService } from "../../../nestJs/modules/payment/payment.service";
import { PubSubService } from "../../../nestJs/modules/pubsub/pubsub.service";
import { StorageService } from "../../../nestJs/modules/storage/storage.service";
import { TransactionService } from "../../../nestJs/modules/transaction/transaction.service";
import { TransactionFactoryService } from "../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";
import { TeamsWebhookService } from "../../../nestJs/modules/teams-webhook/teams-webhook.service";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { DASH_FEE_RECALCULATION } from "../../../nestJs/modules/transaction/dto/txAdjustment.dto";
import { TripDocument } from "../../../nestJs/modules/appDatabase/documents/trip.document";
import decodedIdTokenMock from "../../mockData/firebase/decodedIdToken.mock";
import FakeRepository, {
  mockCreateQueryBuilder,
  mockGetRawOne,
  mockFindOne,
  mockCreateTxAdjustment,
} from "../../utils/services/FakeRepository.specs.utils";
import MockAppDatabaseService from "../../utils/services/FakeAppDatabaseService.specs.utils";
import MockPaymentService from "../../utils/services/FakePaymentService.specs.utils";
import FakeBankService from "../../utils/services/FakeBankService.specs.utils";
import FakeStorageService from "../../utils/services/FakeStorageService.specs.utils";
import FakePubSubService from "../../utils/services/FakePubSubService.specs.utils";
import FakeTransactionFactoryService from "../../utils/services/FakeTransactionFactoryService.specs.utils";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import {
  newTestConfigService,
  newTestEmailService,
  newTestMessageTeamsService,
  newTestSecretService,
  newTestTransactionEventService,
} from "../../utils/services/TestServices.specs.utils";
import { FakeTeamsWebhookService } from "../../utils/services/FakeTeamsWebhookService.specs.utils";

/**
 * Comprehensive test suite for the new adjustTxAndRecalculateFee method
 * 
 * The new implementation uses the formula:
 * adjustedChargeableTotal = tripTotal + additionalBookingFee + fleetBookingFee + dashTips + boostAmount + sumAdjustedPayout + amount
 * dashTransactionFee = dashFeeConstant + dashFeeRate * (adjustedChargeableTotal + dashBookingFee)
 * dashTotalFee = Math.ceil(dashBookingFee + dashTransactionFee)
 * adjustedDashFee = roundUpOneDecimal(dashTotalFee + sumAdjustedDashFee)
 */
describe("TransactionService - adjustTxAndRecalculateFee (New Implementation)", () => {
  let service: TransactionService;
  const utilsService = new UtilsService();

  beforeEach(async () => {
    jest.clearAllMocks();

    // Mock the query builder chain for sumAdjustments
    const mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getRawOne: mockGetRawOne,
    };
    mockCreateQueryBuilder.mockReturnValue(mockQueryBuilder);

    // Mock createTxAdjustment to return proper structure
    mockCreateTxAdjustment.mockImplementation(() => ({ adjustmentTx: new Tx(), parentTx: new Tx() }));

    service = new TransactionService(
      new MockAppDatabaseService() as unknown as AppDatabaseService,
      new FakeRepository() as unknown as MerchantRepository,
      new MockPaymentService() as unknown as PaymentService,
      new FakeRepository() as unknown as TxRepository,
      new FakeRepository() as unknown as TxTagRepository,
      new FakeRepository() as unknown as PayoutRepository,
      new FakeBankService() as unknown as BankService,
      new FakeStorageService() as unknown as StorageService,
      new FakePubSubService() as unknown as PubSubService,
      new FakeTransactionFactoryService() as unknown as TransactionFactoryService,
      utilsService,
      FakeLoggerService,
      new FakeRepository() as unknown as PaymentInstrumentRepository,
      new FakeRepository() as unknown as PaymentTxRepository,
      newTestMessageTeamsService(),
      newTestTransactionEventService(),
      new FakeRepository() as unknown as UserRepository,
      newTestSecretService(),
      newTestEmailService(),
      new FakeRepository() as unknown as DiscountRepository,
      new FakeRepository() as unknown as TxEventRepository,
      newTestConfigService(),
      new FakeTeamsWebhookService() as unknown as TeamsWebhookService,
    );

    // Default: no previous adjustments
    mockGetRawOne.mockResolvedValue({
      sumAdjustedTotal: 0,
      sumAdjustedPayout: 0,
      sumAdjustedDashFee: 0,
    });
  });

  /**
   * Helper function to create mock transactions with proper structure for the new implementation
   */
  function createMockTransaction(params: {
    id: string;
    payoutAmount?: number;
    dashFee?: number;
    total?: number;
    tripTotal?: number;
    dashFeeRate?: number;
    dashFeeConstant?: number;
    dashTips?: number;
    dashBookingFee?: number;
    additionalBookingFee?: number;
    fleetBookingFee?: number;
    boostAmount?: number;
  }): Tx {
    const tx = new Tx();
    tx.id = params.id;
    tx.payoutAmount = params.payoutAmount ?? 0;
    tx.dashFee = params.dashFee ?? 0;
    tx.total = params.total ?? 0;
    
    tx.metadata = {
      tripTotal: params.tripTotal ?? 100,
      billing: {
        dashFeeSettings: {
          dashFeeRate: params.dashFeeRate ?? 0.15,
          dashFeeConstant: params.dashFeeConstant ?? 2.0,
        },
        dashTips: params.dashTips ?? 0,
        dashBookingFee: params.dashBookingFee ?? 1.0,
        additionalBookingFee: params.additionalBookingFee ?? 0,
        fleetBookingFee: params.fleetBookingFee ?? 0,
        boostAmount: params.boostAmount ?? 0,
      },
    } as TripDocument;
    
    return tx;
  }

  describe("Basic Fee Calculation", () => {
    it("should calculate fee correctly with simple parameters", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-1",
        tripTotal: 100,
        dashFeeRate: 0.15,
        dashFeeConstant: 2.0,
        dashBookingFee: 1.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 20,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Basic fee calculation test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(20);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee); // HKD precision
    });

    it("should handle zero adjustment amount", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-2",
        tripTotal: 100,
        dashFeeRate: 0.15,
        dashFeeConstant: 2.0,
        dashBookingFee: 1.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 0,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Zero adjustment test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(0);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.dashFee);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee); // HKD precision
    });

    it("should handle negative adjustment amount", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-3",
        tripTotal: 100,
        dashFeeRate: 0.15,
        dashFeeConstant: 2.0,
        dashBookingFee: 1.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: -30,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Negative adjustment test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(-30);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee); // HKD precision
    });
  });
  describe("Complex Fee Components", () => {
    it("should handle all fee components correctly", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-complex",
        tripTotal: 100,
        dashFeeRate: 0.12,
        dashFeeConstant: 3.0,
        dashTips: 5.0,
        dashBookingFee: 2.0,
        additionalBookingFee: 1.5,
        fleetBookingFee: 2.5,
        boostAmount: 10.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 25,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Complex fee components test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(25);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee!); // HKD precision
    });

    it("should handle zero fee rate correctly", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-zero-rate",
        tripTotal: 100,
        dashFeeRate: 0,
        dashFeeConstant: 5.0,
        dashBookingFee: 1.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 20,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Zero fee rate test",
        },
        decodedIdTokenMock,
      );

      // With zero rate, fee should only be constant + booking fee
      expect(result.payoutAmount).toBe(20);
      expect(result.dashFee).toBe(6.0); // 5.0 constant + 1.0 booking fee
      expect(result.total).toBe(26.0);
    });

    it("should handle zero fee constant correctly", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-zero-constant",
        tripTotal: 100,
        dashFeeRate: 0.15,
        dashFeeConstant: 0,
        dashBookingFee: 1.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 20,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Zero fee constant test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(20);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee!); // HKD precision
    });
  });

  describe("Previous Adjustments Impact", () => {
    it("should handle previous payout adjustments correctly", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-prev-payout",
        tripTotal: 100,
        dashFeeRate: 0.15,
        dashFeeConstant: 2.0,
        dashBookingFee: 1.0,
      });

      // Mock previous adjustments: +30 payout, +4.5 fee
      mockGetRawOne.mockResolvedValueOnce({
        sumAdjustedTotal: 34.5,
        sumAdjustedPayout: 30,
        sumAdjustedDashFee: 4.5,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 10,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Previous adjustments test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(10);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee!); // HKD precision
    });

    it("should handle negative previous adjustments correctly", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-neg-prev",
        tripTotal: 100,
        dashFeeRate: 0.15,
        dashFeeConstant: 2.0,
        dashBookingFee: 1.0,
      });

      // Mock previous adjustments: -20 payout, -3.0 fee
      mockGetRawOne.mockResolvedValueOnce({
        sumAdjustedTotal: -23.0,
        sumAdjustedPayout: -20,
        sumAdjustedDashFee: -3.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 15,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Negative previous adjustments test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(15);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee!); // HKD precision
    });
  });

  describe("HKD Currency Precision", () => {
    it("should maintain HKD precision throughout calculations", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-precision",
        tripTotal: 123.7,
        dashFeeRate: 0.133,
        dashFeeConstant: 2.33,
        dashBookingFee: 1.27,
        dashTips: 3.45,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 27.83,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "HKD precision test",
        },
        decodedIdTokenMock,
      );

      // All results should be rounded to 1 decimal place (HKD precision)
      expect(Number(result.payoutAmount!.toFixed(1))).toBe(result.payoutAmount); // Should be HKD precision
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee); // Should be HKD precision
      expect(Number(result.total!.toFixed(1))).toBe(result.total); // Should be HKD precision
      expect(result.total).toBeCloseTo(result.payoutAmount! + result.dashFee!, 1); // Should sum correctly
    });

    it("should handle fee calculation correctly without Math.ceil", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-no-ceil",
        tripTotal: 100,
        dashFeeRate: 0.15,
        dashFeeConstant: 2.1,
        dashBookingFee: 1.4,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 20,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "No Math.ceil test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(20);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee!); // HKD precision
    });
  });

  describe("Edge Cases", () => {
    it("should handle very small amounts", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-small",
        tripTotal: 0.1,
        dashFeeRate: 0.15,
        dashFeeConstant: 0.1,
        dashBookingFee: 0.1,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 0.1,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Small amounts test",
        },
        decodedIdTokenMock,
      );

      // Should handle minimum HKD denomination correctly
      expect(result.payoutAmount).toBe(0.1);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee!);
    });

    it("should handle large amounts", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-large",
        tripTotal: 9999.9,
        dashFeeRate: 0.15,
        dashFeeConstant: 10.0,
        dashBookingFee: 5.0,
        dashTips: 100.0,
        boostAmount: 200.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 1000.0,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Large amounts test",
        },
        decodedIdTokenMock,
      );

      // Should handle large amounts without precision loss
      expect(result.payoutAmount).toBe(1000.0);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(Number(result.total!.toFixed(1))).toBe(result.total!);
    });

    it("should handle zero trip total", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-zero-trip",
        tripTotal: 0,
        dashFeeRate: 0.15,
        dashFeeConstant: 2.0,
        dashBookingFee: 1.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 50,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Zero trip total test",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(50);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBe(result.payoutAmount! + result.dashFee!);
      expect(Number(result.dashFee!.toFixed(1))).toBe(result.dashFee!); // HKD precision
    });
  });

  describe("Real-world Scenarios", () => {
    it("should handle typical taxi ride with tip", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-taxi-tip",
        tripTotal: 85.0,
        dashFeeRate: 0.12,
        dashFeeConstant: 3.0,
        dashBookingFee: 2.0,
        dashTips: 10.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: 15.0, // Additional tip adjustment
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Taxi ride with tip adjustment",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(15.0);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBeCloseTo(result.payoutAmount! + result.dashFee!, 1);
    });

    it("should handle fleet booking with multiple fees", async () => {
      const parentTx = createMockTransaction({
        id: "parent-tx-fleet",
        tripTotal: 120.0,
        dashFeeRate: 0.10,
        dashFeeConstant: 2.5,
        dashBookingFee: 3.0,
        additionalBookingFee: 5.0,
        fleetBookingFee: 8.0,
        boostAmount: 15.0,
      });

      mockFindOne.mockResolvedValueOnce(parentTx);
      mockCreateTxAdjustment.mockResolvedValueOnce({ adjustmentTx: new Tx(), parentTx });

      const result = await service.dryRunAdjustment(
        parentTx.id,
        {
          amount: -25.0, // Discount adjustment
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "Fleet booking discount",
        },
        decodedIdTokenMock,
      );

      expect(result.payoutAmount).toBe(-25.0);
      expect(result.dashFee).toBeGreaterThan(0);
      expect(result.total).toBeCloseTo(result.payoutAmount! + result.dashFee!, 1);
    });
  });
});
