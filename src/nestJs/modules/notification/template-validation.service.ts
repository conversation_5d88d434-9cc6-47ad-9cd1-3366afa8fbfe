import { Injectable } from "@nestjs/common";

export interface TemplateValidationResult {
  isValid: boolean;
  missingVariables: string[];
  foundVariables: string[];
  availableColumns: string[];
}

@Injectable()
export class TemplateValidationService {

  /**
   * Validate a single template against available columns
   */
  validateTemplate(template: string, availableColumns: string[]): TemplateValidationResult {
    const templateVariables = this.extractTemplateVariables(template);
    const missingVariables: string[] = [];

    // Check if template variables exist in available columns
    templateVariables.forEach(variable => {
      if (!availableColumns.includes(variable)) {
        missingVariables.push(variable);
      }
    });

    return {
      isValid: missingVariables.length === 0,
      missingVariables,
      foundVariables: templateVariables,
      availableColumns,
    };
  }

  /**
   * Extract variables from template string (e.g., {{name}} -> ['name'])
   */
  extractTemplateVariables(template: string): string[] {
    if (!template) return [];

    const regex = /\{\{(\w+)\}\}/g;
    const variables: string[] = [];
    let match;

    while ((match = regex.exec(template)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1]);
      }
    }

    return variables;
  }

  /**
   * Validate multiple templates at once
   */
  validateAllTemplates(templates: Record<string, string>, availableColumns: string[]): TemplateValidationResult {
    let allVariables: string[] = [];
    let allMissingVariables: string[] = [];

    Object.entries(templates).forEach(([key, template]) => {
      if (template) {
        const result = this.validateTemplate(template, availableColumns);
        allVariables = [...new Set([...allVariables, ...result.foundVariables])];
        allMissingVariables = [...new Set([...allMissingVariables, ...result.missingVariables])];
      }
    });

    return {
      isValid: allMissingVariables.length === 0,
      missingVariables: allMissingVariables,
      foundVariables: allVariables,
      availableColumns,
    };
  }

  /**
   * Resolve template variables with actual data
   */
  resolveTemplate(template: string, data: Record<string, any>): string {
    if (!template) return template;

    return template.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
      const value = data[variable];
      return value !== undefined ? String(value) : "";
    });
  }

  /**
   * Validate templates against recipient data structure
   */
  validateTemplatesWithRecipients(
    templates: Record<string, string>,
    recipients: Array<Record<string, any>>
  ): TemplateValidationResult {
    if (!recipients || recipients.length === 0) {
      throw new Error("Recipients array is empty");
    }

    const availableColumns = Array.from(
      new Set(
        recipients.flatMap(recipient => Object.keys(recipient))
      )
    );

    return this.validateAllTemplates(templates, availableColumns);
  }

  /**
   * Check if template contains variables
   */
  hasTemplateVariables(template: string): boolean {
    if (!template) return false;
    return /\{\{\w+\}\}/.test(template);
  }

  /**
   * Resolve all templates in notification request with recipient data
   */
  resolveNotificationTemplates(
    notificationRequest: any,
    recipientData: Record<string, any>
  ): any {
    if (!notificationRequest) return notificationRequest;

    return {
      ...notificationRequest,
      titleEn: this.resolveTemplate(notificationRequest.titleEn, recipientData),
      bodyEn: this.resolveTemplate(notificationRequest.bodyEn, recipientData),
      titleHk: this.resolveTemplate(
        notificationRequest.titleHk || notificationRequest.titleEn,
        recipientData
      ),
      bodyHk: this.resolveTemplate(
        notificationRequest.bodyHk || notificationRequest.bodyEn,
        recipientData
      ),
      ctaEn: this.resolveTemplate(notificationRequest.ctaEn, recipientData),
      ctaHk: this.resolveTemplate(
        notificationRequest.ctaHk || notificationRequest.ctaEn,
        recipientData
      ),
      imageEn: notificationRequest.imageEn,
      imageHk: notificationRequest.imageHk,
    };
  }

  /**
   * Extract phone numbers from recipients using flexible detection
   */
  extractPhoneNumbers(recipients: Array<Record<string, any>>): string[] {
    const phoneNumbers: string[] = [];
    const hongKongPhoneRegex = /^\+85[0-9]{8,9}$/; // Hong Kong phone format: +85 followed by 8-9 digits

    recipients.forEach((recipient, index) => {
      let foundPhone: string | null = null;

      const commonPhoneColumns = ["phone", "phoneNumber", "contactNumber", "mobile", "cellphone", "tel", "telephone"];

      for (const column of commonPhoneColumns) {
        if (recipient[column] && typeof recipient[column] === "string") {
          const cleanPhone = recipient[column].trim();
          if (hongKongPhoneRegex.test(cleanPhone)) {
            foundPhone = cleanPhone;
            break;
          }
        }
      }

      if (!foundPhone) {
        for (const [key, value] of Object.entries(recipient)) {
          if (value && typeof value === "string") {
            const cleanValue = value.trim();
            if (hongKongPhoneRegex.test(cleanValue)) {
              foundPhone = cleanValue;
              console.log(`🔍 [TemplateValidationService] Found phone number in column '${key}': ${foundPhone}`);
              break;
            }
          }
        }
      }

      if (foundPhone) {
        phoneNumbers.push(foundPhone);
      } else {
        console.warn(`⚠️ [TemplateValidationService] No valid Hong Kong phone number found for recipient ${index + 1}:`, recipient);
      }
    });

    return phoneNumbers;
  }

  /**
   * Extract a single phone number from a recipient object
   */
  extractPhoneFromRecipient(recipient: Record<string, any>): string | null {
    const phoneNumbers = this.extractPhoneNumbers([recipient]);
    return phoneNumbers.length > 0 ? phoneNumbers[0] : null;
  }
}