import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateFraudWhitelistTable1755226236757 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS fraud_whitelist (
                id SERIAL PRIMARY KEY,
                phone_number VARCHAR(32) NOT NULL UNIQUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP TABLE IF EXISTS fraud_whitelist;
        `);
    }
}