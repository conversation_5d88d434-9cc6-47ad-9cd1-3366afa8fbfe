import { randomUUID } from "crypto";

import { Injectable } from "@nestjs/common";
import * as jwt from "jsonwebtoken";

import { Log } from "@nest/decorators/log.decorator";

import LoggerServiceAdapter from "../utils/logger/logger.service";

export interface JwtPayload {
  [key: string]: unknown;
}

export interface JwtOptions {
  expiresIn?: jwt.SignOptions["expiresIn"];
  issuer?: string;
  audience?: string;
  subject?: string;
  algorithm?: "ES256" | "ES384" | "ES512";
  jwtid?: string;
}

@Injectable()
export class JwtService {
  constructor(private readonly logger: LoggerServiceAdapter) {}
  /**
   * Generate a JWT token using Elliptic Curve algorithm
   * @param privateKey - EC private key in PEM format
   * @param payload - The payload to encode in the JWT
   * @param options - Additional JWT options
   * @returns The generated JWT token
   */
  generateToken(privateKey: string, payload: JwtPayload, options: JwtOptions = {}): string {
    const { expiresIn = "1h", issuer, audience, subject, algorithm = "ES256", jwtid = randomUUID() } = options;

    const signOptions: jwt.SignOptions = {
      algorithm,
      expiresIn,
      notBefore: "0",
      jwtid: jwtid,
      ...(issuer && { issuer }),
      ...(audience && { audience }),
      ...(subject && { subject }),
    };

    try {
      return jwt.sign(payload, privateKey, signOptions);
    } catch (error) {
      throw new Error(`Failed to generate JWT: ${(error as Error).message}`);
    }
  }

  /**
   * Verify a JWT token using Elliptic Curve algorithm
   * @param token - The JWT token to verify
   * @param publicKey - EC public key in PEM format
   * @param options - Verification options
   * @returns The decoded payload
   */
  @Log()
  verifyToken(token: string, publicKey: string, options: Omit<JwtOptions, "expiresIn"> = {}): JwtPayload {
    const { issuer, audience, subject, algorithm = "ES256" } = options;

    const verifyOptions: jwt.VerifyOptions = {
      algorithms: [algorithm],
      ...(issuer && { issuer }),
      ...(audience && { audience }),
      ...(subject && { subject }),
    };

    try {
      return jwt.verify(token, publicKey, verifyOptions) as JwtPayload;
    } catch (error) {
      throw new Error(`Failed to verify JWT: ${(error as Error).message}`);
    }
  }

  /**
   * Decode a JWT token without verification (useful for inspecting tokens)
   * @param token - The JWT token to decode
   * @returns The decoded payload and header
   */
  @Log()
  decodeToken(token: string): {
    header: jwt.JwtHeader;
    payload: JwtPayload;
    signature: string;
  } | null {
    try {
      const decoded = jwt.decode(token, { complete: true });
      if (!decoded) {
        return null;
      }

      return {
        header: decoded.header,
        payload: decoded.payload as JwtPayload,
        signature: decoded.signature as string,
      };
    } catch (error) {
      this.logger.error(
        "Failed to decode JWT",
        {
          token,
        },
        error as Error,
      );
      return null;
    }
  }
}
