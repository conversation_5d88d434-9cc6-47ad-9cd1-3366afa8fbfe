import { Test, TestingModule } from "@nestjs/testing";

import { CreateHailingRequestProcessor } from "@nest/modules/v2/hailing/processors/create-hailing-request.processor";
import { MockCreateFleetOrderDelegatee } from "@tests/v2/mockModules/mockMeTaxiModule";
import { MockClsContextStorageServiceProvider } from "@tests/v2/mockModules/mockClsModule";
import { MockPaymentServiceProvider } from "@tests/v2/mockModules/mockPaymentModule";
import { MockHailingApiServiceProvider } from "@tests/v2/mockModules/mockHailingApiModule";
import { MockPubsubServiceProvider } from "@tests/v2/mockModules/mockPubsubModule";
import { MockUserRepository } from "@tests/v2/mockModules/mockUserModule";
import { MockCampaignServiceProvider } from "@tests/v2/mockModules/mockCampaignModule";
import { MockAppDatabaseServiceProvider } from "@tests/v2/mockModules/mockAppDatabaseModule";
import { MockHailingV2ServiceProvider } from "@tests/v2/mockModules/mockHailingV2Module";
import { LocalizedLanguage } from "@nest/modules/location/dto/location.dto";
import { PlatformType, ClientType } from "@nest/modules/v2/hailing/dto/create-hail.dto";
import { MockTxRepositoryProvider } from "@tests/v2/mockModules/nockTxModule";
import { MockTxAppRepositoryProvider } from "@tests/v2/mockModules/mockTxAppModule";
import { MockLocationServiceProvider } from "@tests/v2/mockModules/mockLocationModule";
import { UtilsModule } from "@nest/modules/utils/utils.module";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { mockUserRepositoryFactory } from "@tests/v2/mockFactories/mockUserRepositoryFactory";
import PaymentInstrument from "@nest/modules/database/entities/paymentInstrument.entity";
import { PaymentInstrumentState } from "@nest/modules/payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { HailingV2Service } from "@nest/modules/v2/hailing/services/hailing-v2.service";
import { mockHailingV2ServiceFactory } from "@tests/v2/mockFactories/mockHailingV2ServiceFactory";
import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { PubSubService } from "@nest/modules/pubsub/pubsub.service";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { CreateFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/CreateFleetOrderDelegatee";
import { MockLoggerServiceAdapterProvider } from "@tests/v2/mockModules/mockLoggerServiceAdapter";
import { mockHailingApiServiceFactory } from "@tests/v2/mockFactories/mockHailingApiServiceFactory";
import { ConfigModule } from "@nestjs/config";
import { MockPriceRuleServiceProvider } from "@tests/v2/mockModules/mockPriceRuleModule";

describe("CreateHailingRequestProcessor", () => {
  let processor: CreateHailingRequestProcessor;
  let userRepository: UserRepository;
  let hailingV2Service: HailingV2Service;
  let txRepository: TxRepository;
  let pubsubService: PubSubService;
  let hailingApiService: HailingApiService;
  let createFleetOrderDelegatee: CreateFleetOrderDelegatee;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [UtilsModule.forRoot(), ConfigModule.forRoot()],
      providers: [
        MockLoggerServiceAdapterProvider,
        CreateHailingRequestProcessor,
        MockClsContextStorageServiceProvider,
        MockCreateFleetOrderDelegatee,
        MockPaymentServiceProvider,
        MockHailingApiServiceProvider,
        MockPubsubServiceProvider,
        MockUserRepository,
        MockCampaignServiceProvider,
        MockHailingV2ServiceProvider,
        MockAppDatabaseServiceProvider,
        MockTxRepositoryProvider,
        MockTxAppRepositoryProvider,
        MockLocationServiceProvider,
        MockPriceRuleServiceProvider,
      ],
      controllers: [],
      exports: [],
    }).compile();

    processor = moduleRef.get(CreateHailingRequestProcessor);
    userRepository = moduleRef.get(UserRepository);
    hailingV2Service = moduleRef.get(HailingV2Service);
    txRepository = moduleRef.get(TxRepository);
    pubsubService = moduleRef.get(PubSubService);
    hailingApiService = moduleRef.get(HailingApiService);
    createFleetOrderDelegatee = moduleRef.get(CreateFleetOrderDelegatee);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const paymentInstrumentId = "5b9f6b4f-6259-4410-8c88-e4c92a498d5b";

  const request = {
    quoteId: "6cf2d323-8138-4a38-8230-c43a1925d40f",
    language: LocalizedLanguage.EN,
    platformType: PlatformType.DASH,
    itinerary: [
      {
        index: 0,
        placeId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
        lng: 114.18396337853324,
        lat: 22.279593203640275,
        displayName: "Hysan Place",
        formattedAddress: "Hysan Place, 500 Hennessy Rd, Causeway Bay",
      },
      {
        index: 1,
        placeId: "ChIJE-FWMvMDBDQRBCxQzoVA-Ao",
        lng: 114.26161142551531,
        lat: 22.306779062030994,
        displayName: "TKO Plaza",
        formattedAddress: "Tseung Kwan O Plaza Club House, 1 Tong Tak St, Tseung Kwan O",
      },
    ],
    isDoubleTunnelFee: true,
    paymentInstrumentId,
    options: {
      isAssistant: true,
      isPetFriendly: true,
    },
    clientType: ClientType.DASH,
  };

  describe(".execute", () => {
    describe("when platformType is DASH and ClientType is DASH", () => {
      it("should create a hailing request", async () => {
        const paymentInstrumentId = "5b9f6b4f-6259-4410-8c88-e4c92a498d5b";
        mockHailingApiServiceFactory.mockGetPriceEstimationV3(hailingApiService);
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository, {
          paymentInstruments: [
            {
              id: paymentInstrumentId,
              isPreferred: true,
              state: PaymentInstrumentState.VERIFIED,
              paymentGateway: "GLOBAL_PAYMENTS",
              expirationDate: new Date("2124-08-05T03:55:39.009Z"),
            },
          ] as PaymentInstrument[],
        });
        mockHailingV2ServiceFactory.mockGetValidPaymentInstrument(hailingV2Service);
        jest.spyOn(processor, "processPreAuth").mockImplementation(async () => {
          return {
            id: "123",
          } as unknown as PaymentTx;
        });

        await expect(processor.execute(request, {} as any, "123")).resolves.not.toThrow();
        expect(txRepository.save).toHaveBeenCalled();
        expect(pubsubService.publishMessageForHailingTxCreated).toHaveBeenCalled();
        expect(hailingApiService.getPriceEstimationV3).toHaveBeenCalled();
        expect(hailingApiService.createHailingOrderV3).toHaveBeenCalled();
        expect(createFleetOrderDelegatee.execute).not.toHaveBeenCalled();
      });

      it("if no payment instrument is provided should throw an error", async () => {
        await expect(
          processor.execute(
            {
              ...request,
              paymentInstrumentId: undefined,
            },
            {} as any,
            "123",
          ),
        ).rejects.toThrow();
      });
    });

    describe("when platformType is FLEET and ClientType is DASH", () => {
      it("should create a hailing request", async () => {
        const paymentInstrumentId = "5b9f6b4f-6259-4410-8c88-e4c92a498d5b";
        mockHailingApiServiceFactory.mockGetPriceEstimationV3(hailingApiService);
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository, {
          paymentInstruments: [
            {
              id: paymentInstrumentId,
              isPreferred: true,
              state: PaymentInstrumentState.VERIFIED,
              paymentGateway: "GLOBAL_PAYMENTS",
              expirationDate: new Date("2124-08-05T03:55:39.009Z"),
            },
          ] as PaymentInstrument[],
        });
        mockHailingV2ServiceFactory.mockGetValidPaymentInstrument(hailingV2Service);
        jest.spyOn(processor, "processPreAuth").mockImplementation(async () => {
          return {
            id: "123",
          } as unknown as PaymentTx;
        });

        await expect(
          processor.execute(
            {
              ...request,
              platformType: PlatformType.FLEET,
            },
            {} as any,
            "123",
          ),
        ).resolves.not.toThrow();
        expect(txRepository.save).toHaveBeenCalled();
        expect(pubsubService.publishMessageForHailingTxCreated).toHaveBeenCalled();
        expect(hailingApiService.getPriceEstimationV3).toHaveBeenCalled();
        expect(hailingApiService.createHailingOrderV3).toHaveBeenCalled();
        expect(createFleetOrderDelegatee.execute).toHaveBeenCalled();
      });
    });

    describe("when platformType is DASH and ClientType is B2B_APP", () => {
      it("should create a hailing request but not calling createHailingOrderV3", async () => {
        const paymentInstrumentId = "5b9f6b4f-6259-4410-8c88-e4c92a498d5b";
        mockHailingApiServiceFactory.mockGetPriceEstimationV3(hailingApiService);
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository, {
          paymentInstruments: [
            {
              id: paymentInstrumentId,
              isPreferred: true,
              state: PaymentInstrumentState.VERIFIED,
              paymentGateway: "GLOBAL_PAYMENTS",
              expirationDate: new Date("2124-08-05T03:55:39.009Z"),
            },
          ] as PaymentInstrument[],
        });
        mockHailingV2ServiceFactory.mockGetValidPaymentInstrument(hailingV2Service);
        jest.spyOn(processor, "processPreAuth").mockImplementation(async () => {
          return {
            id: "123",
          } as unknown as PaymentTx;
        });

        await expect(
          processor.execute(
            {
              ...request,
              clientType: ClientType.B2B_APP,
            },
            {} as any,
            "123",
          ),
        ).resolves.not.toThrow();
        expect(txRepository.save).toHaveBeenCalled();
        expect(pubsubService.publishMessageForHailingTxCreated).not.toHaveBeenCalled();
        expect(hailingApiService.getPriceEstimationV3).toHaveBeenCalled();
        expect(hailingApiService.createHailingOrderV3).not.toHaveBeenCalled();
        expect(createFleetOrderDelegatee.execute).not.toHaveBeenCalled();
      });

      it("when no payment instrument is provided should return successfully", async () => {
        await expect(
          processor.execute(
            {
              ...request,
              clientType: ClientType.B2B_APP,
              paymentInstrumentId: undefined,
            },
            {} as any,
            "123",
          ),
        ).resolves.not.toThrow();

        expect(txRepository.save).toHaveBeenCalled();
        expect(pubsubService.publishMessageForHailingTxCreated).not.toHaveBeenCalled();
        expect(hailingApiService.getPriceEstimationV3).toHaveBeenCalled();
        expect(hailingApiService.createHailingOrderV3).not.toHaveBeenCalled();
        expect(createFleetOrderDelegatee.execute).not.toHaveBeenCalled();
      });
    });
  });
});
