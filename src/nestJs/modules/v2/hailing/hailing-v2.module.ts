import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { HailingApiModule } from "@nest/modules/hailingApi/hailingApi.module";
import { CampaignModule } from "@nest/modules/campaign/campaign.module";
import { LocationModule } from "@nest/modules/location/location.module";
import { PaymentModule } from "@nest/modules/payment/payment.module";
import { PubSubModule } from "@nest/modules/pubsub/pubsub.module";
import { TxAppRepository } from "@nest/modules/database/repositories/app.repository";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { MeFleetTaxiModule } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.module";
import { TransactionEventModule } from "@nest/modules/transaction/modules/transactionEvent.module";

import { PriceRuleModule } from "../priceRule/price.rule.module";

import { HailingV2Controller } from "./hailing-v2.controller";
import { CreateHailingRequestProcessor } from "./processors/create-hailing-request.processor";
import { CreateHailingQuoteProcessor } from "./processors/create-hailing-quote.processor";
import { HailingV2Service } from "./services/hailing-v2.service";
import { ConfirmHailingRequestProcessor } from "./processors/confirm-hailing-request.processor";
import { CancelHailingRequestProcessor } from "./processors/cancel-hailing-request.processor";

@Module({
  imports: [
    PaymentModule,
    LocationModule,
    CampaignModule,
    PubSubModule,
    HailingApiModule,
    MeFleetTaxiModule,
    TransactionEventModule,
    ConfigModule,
    PriceRuleModule,
  ],
  providers: [
    HailingV2Controller,
    CreateHailingRequestProcessor,
    CreateHailingQuoteProcessor,
    ConfirmHailingRequestProcessor,
    CancelHailingRequestProcessor,
    PaymentTxRepository,
    TxRepository,
    TxAppRepository,
    UserRepository,
    HailingV2Service,
  ],
  exports: [
    HailingV2Service,
    CreateHailingRequestProcessor,
    CreateHailingQuoteProcessor,
    ConfirmHailingRequestProcessor,
    CancelHailingRequestProcessor,
  ],
  controllers: [HailingV2Controller],
})
export class HailingV2Module {}
