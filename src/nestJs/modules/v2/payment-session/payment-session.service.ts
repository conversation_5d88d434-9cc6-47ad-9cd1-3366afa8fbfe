import { randomUUID } from "crypto";

import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import dayjs from "dayjs";

import { Log } from "@nest/decorators/log.decorator";
import { PublicHailingV2Service } from "@nest/modules/v2/public-hailing/services/public-hailing-v2.service";
import { JwtService } from "@nest/modules/jwt/jwt.service";
import { TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { SecretsService } from "@nest/modules/secrets/secrets.service";
import { Secret } from "@nest/modules/secrets/types";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { CreatePaymentSessionRequestDto, CreatePaymentSessionResponseDto } from "./dto/create-payment-session.dto";

@Injectable()
export class PaymentSessionService {
  private readonly TOKEN_AUDIENCE = "kraken";
  private readonly TOKEN_VALIDITY_VALUE = 1;
  private readonly TOKEN_VALIDITY_UNIT = "h";

  constructor(
    private readonly krakenMerchantId: string,
    private readonly privateKey: string,
    private readonly publicHailingV2Service: PublicHailingV2Service,
    private readonly jwtService: JwtService,
  ) {}

  @Log()
  private createPaymentSession(
    createPaymentSessionRequest: CreatePaymentSessionRequestDto,
  ): CreatePaymentSessionResponseDto {
    const sessionId = randomUUID();
    const validUntilInSeconds = dayjs().add(this.TOKEN_VALIDITY_VALUE, this.TOKEN_VALIDITY_UNIT).unix();
    const paymentToken = this.jwtService.generateToken(
      this.privateKey,
      {
        claims: {
          ...createPaymentSessionRequest,
          act: "payment",
        },
      },
      {
        expiresIn: `${this.TOKEN_VALIDITY_VALUE}${this.TOKEN_VALIDITY_UNIT}`,
        algorithm: "ES256",
        audience: this.TOKEN_AUDIENCE,
        issuer: this.krakenMerchantId,
        subject: createPaymentSessionRequest.txId,
        jwtid: sessionId,
      },
    );

    return {
      token: paymentToken,
      sessionId,
      request: createPaymentSessionRequest,
      validUntil: validUntilInSeconds,
    };
  }

  @Log()
  async createTxSessionForPayment(txId: string): Promise<CreatePaymentSessionResponseDto> {
    const tx = await this.publicHailingV2Service.getHailingRequest(txId);

    if (tx.status !== TxHailingRequestStatus.PENDING_PAYMENT) {
      throw errorBuilder.transaction.notPendingPayment(tx.id, tx.status);
    }

    if (!tx.user) {
      throw errorBuilder.transaction.txHasNoUser(tx.id);
    }

    return this.createPaymentSession({
      txId: tx.id,
      userId: tx.user.id,
      amount: tx.total,
      currency: "HKD", // hard-coded for now
    });
  }

  static async loadConfigs(
    secretsService: SecretsService,
    config: ConfigService,
    publicHailing: PublicHailingV2Service,
    jwt: JwtService,
  ) {
    const privateKey = await secretsService.getSecret(Secret.PAYMENT_SESSION_PRIVATE_KEY);
    const krakenMerchantId = config.getOrThrow<string>("KRAKEN_MERCHANT_ID");
    return new PaymentSessionService(krakenMerchantId, privateKey, publicHailing, jwt);
  }
}
