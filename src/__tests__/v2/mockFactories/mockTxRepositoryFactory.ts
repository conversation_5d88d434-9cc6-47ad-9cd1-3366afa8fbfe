/**
 * Mock Transaction Repository Factory
 * 
 * This factory provides comprehensive fake payload generators for transaction entities.
 * It includes mock data for all transaction types (TRIP, HAILING_REQUEST, TX_ADJUSTMENT, CARD_VERIFICATION)
 * along with related entities like PaymentTx, TxEvent, and TxTag.
 * 
 * Usage Examples:
 * 
 * 1. Mock repository methods:
 *    mockTxRepositoryFactory.mockTxFindOne(txRepository, { total: 150.00 });
 * 
 * 2. Generate fake transaction payloads:
 *    const tripTx = mockTxRepositoryFactory.createFakeTripTx();
 *    const hailingTx = mockTxRepositoryFactory.createFakeHailingRequestTx();
 *    const adjustmentTx = mockTxRepositoryFactory.createFakeAdjustmentTx();
 * 
 * 3. Generate full transaction with related entities:
 *    const fullTx = mockTxRepositoryFactory.createFullTxPayload(TxTypes.TRIP);
 * 
 * 4. Override specific properties:
 *    const customTx = mockTxRepositoryFactory.createFakeTripTx({
 *      total: 200.00,
 *      user: { id: "specific-user-id" }
 *    });
 * 
 * All generators accept an optional overrides parameter to customize the generated data.
 */

import { randomUUID } from "crypto";
import { TxTypes } from "../../../nestJs/modules/transaction/dto/txType.dto";
import { TxAppsNames } from "../../../nestJs/modules/apps/dto/Apps.dto";
import { TxPayoutStatus } from "../../../nestJs/modules/transaction/dto/txPayoutStatus.dto";
import { TxHailingRequestStatus } from "../../../nestJs/modules/transaction/dto/txHailingRequest.dto";
import { PaymentGatewayTypes } from "../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { TxEventType } from "../../../nestJs/modules/transaction/dto/txEventType.dto";
import { TxTagType } from "../../../nestJs/modules/transaction/dto/txTagType.dto";
import { PlatformType } from "../../../nestJs/modules/v2/hailing/dto/create-hail.dto";

export const mockTxRepositoryFactory = {
  mockHailingTxFindOne: (txRepository: any, tx: Partial<any> = {}, isReturnNull = false) => {
    jest.spyOn(txRepository, "findOne").mockImplementation(async () => {
      if (isReturnNull) {
        return null;
      }
      return {
        ...mockTxRepositoryFactory.createFakeHailingRequestTx(),
        ...tx,
      }
    });
  },
  mockTxFindOneBy: (txRepository: any, tx: Partial<any> = {}) => {
    jest.spyOn(txRepository, "findOneBy").mockImplementation(async () => {
      return {
        id: "123",
        ...tx,
      }
    });
  },

  // Fake payload generators for different transaction types
  createFakeTripTx: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    return {
      id: randomUUID(),
      type: TxTypes.TRIP,
      txApp: {
        id: randomUUID(),
        name: TxAppsNames.TAPXI,
        createdAt: baseDate,
        updatedAt: baseDate,
      },
      merchant: {
        id: randomUUID(),
        name: "Test Taxi Company",
        phoneNumber: "+85291234567",
      },
      payoutMerchant: {
        id: randomUUID(),
        name: "Test Payout Merchant",
      },
      user: {
        id: randomUUID(),
        phoneNumber: "+85298765432",
      },
      metadata: {
        tripId: randomUUID(),
        tripStart: baseDate,
        tripEnd: new Date(baseDate.getTime() + 30 * 60 * 1000), // 30 minutes later
        pickupLocation: {
          address: "Central, Hong Kong",
          latitude: 22.2783,
          longitude: 114.1747,
        },
        dropoffLocation: {
          address: "Tsim Sha Tsui, Hong Kong", 
          latitude: 22.2976,
          longitude: 114.1722,
        },
        distance: 5.2,
        duration: 1800, // 30 minutes
      },
      total: 120.50,
      dashFee: 12.05,
      payoutAmount: 108.45,
      discount: 0,
      discountThirdParty: 0,
      discountDash: 0,
      bonus: 0,
      payoutStatus: TxPayoutStatus.RELEASED,
      createdAt: baseDate,
      updatedAt: baseDate,
      createdBy: "SYSTEM",
      ...overrides,
    };
  },

  createFakeHailingRequestTx: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    const fakeTx = {
      id: randomUUID(),
      type: TxTypes.HAILING_REQUEST,
      txApp: {
        id: randomUUID(),
        name: TxAppsNames.TAPXI,
        createdAt: baseDate,
        updatedAt: baseDate,
      },
      user: {
        id: randomUUID(),
        phoneNumber: "+85298765432",
      },
      metadata: {
        type: "HAIL",
        platformType: PlatformType.DASH,
        status: TxHailingRequestStatus.PENDING,
        request: {
          platformType: PlatformType.DASH,
          pickup: {
            address: "Central Station, Hong Kong",
            latitude: 22.2783,
            longitude: 114.1747,
          },
          dropoff: {
            address: "Airport Express Hong Kong Station",
            latitude: 22.2849,
            longitude: 114.1583,
          },
          time: baseDate,
        },
        itinerary: [
          {
            address: "Central Station, Hong Kong",
            latitude: 22.2783,
            longitude: 114.1747,
            type: "pickup",
          },
          {
            address: "Airport Express Hong Kong Station", 
            latitude: 22.2849,
            longitude: 114.1583,
            type: "dropoff",
          },
        ],
        userPhoneNumber: "+85298765432",
        time: baseDate,
        charges: {
          cancellationFee: 0,
        },
        discounts: {
          discountIdThirdParty: "",
          discountRulesThirdParty: "",
          discountIdDash: "",
          discountRulesDash: "",
        },
      },
      total: 0,
      createdAt: baseDate,
      updatedAt: baseDate,
      createdBy: "USER",
      txEvents: [
        {
          id: randomUUID(),
          type: TxEventType.HAILING_USER_CREATES_ORDER,
          createdBy: "USER",
          createdAt: baseDate,
          updatedAt: baseDate,
        },
      ],
      ...overrides,
    };
    return fakeTx;
  },

  createFakeAdjustmentTx: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    return {
      id: randomUUID(),
      type: TxTypes.TX_ADJUSTMENT,
      txApp: {
        id: randomUUID(),
        name: TxAppsNames.TAPXI,
        createdAt: baseDate,
        updatedAt: baseDate,
      },
      parentTx: {
        id: randomUUID(),
        type: TxTypes.TRIP,
      },
      merchant: {
        id: randomUUID(),
        name: "Test Taxi Company",
      },
      user: {
        id: randomUUID(),
        phoneNumber: "+85298765432",
      },
      metadata: {
        reason: "Fare adjustment due to traffic congestion",
        dashFeeRate: 0.1,
        dashFeeConstant: 5.0,
        billing: {
          discount: 0,
          dashFeeSettings: {},
        },
      },
      total: 25.00,
      dashFee: 2.50,
      payoutAmount: 22.50,
      adjustment: 25.00,
      createdAt: baseDate,
      updatedAt: baseDate,
      createdBy: "ADMIN",
      ...overrides,
    };
  },

  createFakeCardVerificationTx: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    return {
      id: randomUUID(),
      type: TxTypes.CARD_VERIFICATION,
      txApp: {
        id: randomUUID(),
        name: TxAppsNames.TAPXI,
        createdAt: baseDate,
        updatedAt: baseDate,
      },
      user: {
        id: randomUUID(),
        phoneNumber: "+85298765432",
      },
      metadata: {
        instrumentIdentifier: "card_12345",
        paymentGateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
        userAppDatabaseId: randomUUID(),
        sessionId: "session_" + randomUUID(),
        isPayerAuthEnroled: true,
        token: "token_" + randomUUID(),
      },
      total: 1.00, // Verification amount
      createdAt: baseDate,
      updatedAt: baseDate,
      createdBy: "USER",
      ...overrides,
    };
  },

  createFakePaymentTx: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    return {
      id: randomUUID(),
      amount: 120.50,
      gatewayTransactionId: "gw_tx_" + randomUUID(),
      cardNumber: "****1234",
      paymentMethod: "VISA",
      tx: {
        id: randomUUID(),
        txApp: {
          id: randomUUID(),
          name: TxAppsNames.TAPXI,
        },
      },
      gateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
      gatewayResponse: {
        status: "SUCCESS",
        transactionId: "gw_tx_" + randomUUID(),
        authCode: "AUTH123",
      },
      status: PaymentInformationStatus.SUCCESS,
      type: PaymentInformationType.SALE,
      requestedBy: "USER",
      createdAt: baseDate,
      updatedAt: baseDate,
      ...overrides,
    };
  },

  createFakeTxEvent: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    return {
      id: randomUUID(),
      tx: {
        id: randomUUID(),
      },
      type: TxEventType.HAILING_USER_CREATES_ORDER,
      content: {},
      createdBy: "USER",
      createdAt: baseDate,
      updatedAt: baseDate,
      ...overrides,
    };
  },

  createFakeTxTag: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    return {
      id: randomUUID(),
      tx: {
        id: randomUUID(),
      },
      tag: TxTagType.DISPUTE,
      note: "Transaction disputed by customer",
      createdBy: "ADMIN",
      createdAt: baseDate,
      updatedAt: baseDate,
      removedAt: null,
      ...overrides,
    };
  },

  // Utility to create full transaction with related entities
  createFullTxPayload: (type: TxTypes = TxTypes.TRIP, overrides: Partial<any> = {}) => {
    let baseTx: any;
    
    switch (type) {
      case TxTypes.TRIP:
        baseTx = mockTxRepositoryFactory.createFakeTripTx();
        break;
      case TxTypes.HAILING_REQUEST:
        baseTx = mockTxRepositoryFactory.createFakeHailingRequestTx();
        break;
      case TxTypes.TX_ADJUSTMENT:
        baseTx = mockTxRepositoryFactory.createFakeAdjustmentTx();
        break;
      case TxTypes.CARD_VERIFICATION:
        baseTx = mockTxRepositoryFactory.createFakeCardVerificationTx();
        break;
      default:
        baseTx = mockTxRepositoryFactory.createFakeTripTx();
    }

    return {
      ...baseTx,
      paymentTx: [
        mockTxRepositoryFactory.createFakePaymentTx({
          tx: { id: baseTx.id },
          amount: baseTx.total,
        }),
      ],
      txEvents: baseTx.txEvents || [
        mockTxRepositoryFactory.createFakeTxEvent({
          tx: { id: baseTx.id },
        }),
      ],
      txTag: [],
      ...overrides,
    };
  },
};