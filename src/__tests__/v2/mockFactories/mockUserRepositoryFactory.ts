import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import User from "@nest/modules/database/entities/user.entity";
import { AppUser } from "@nest/modules/user/dto/user.dto";

const mockUserFindOne = (userRepository: UserRepository, user: Partial<User> = {}) => {
  jest.spyOn(userRepository, "findOne").mockImplementation(async () => {
    return {
      id: "123",
      phoneNumber: "123",
      ...user,
    } as unknown as User;
  });
};

const mockUserFindAppUserById = (userRepository: UserRepository, user: Partial<User> = {}) => {
  jest.spyOn(userRepository, "findAppUserById").mockImplementation(async () => {
    return {
      id: "123",
      appDatabaseId: "123",
      salt: "123",
      publicKey: "123",
      privateKey: "123",
      paymentInstruments: [
        {
          id: user.paymentInstruments?.[0]?.id || "123",
          isPreferred: true,
          state: "VERIFIED",
          paymentGateway: "GLOBAL_PAYMENTS",
        },
      ],
      ...user,
    } as unknown as AppUser;
  });
};

export const mockUserRepositoryFactory = {
  mockUserFindOne,
  mockUserFindAppUserById,
};
