import { Test, TestingModule } from "@nestjs/testing";

import { MockCreateFleetOrderDelegatee } from "@tests/v2/mockModules/mockMeTaxiModule";
import { MockClsContextStorageServiceProvider } from "@tests/v2/mockModules/mockClsModule";
import { MockPaymentServiceProvider, MockPaymentTxRepositoryProvider } from "@tests/v2/mockModules/mockPaymentModule";
import { MockHailingApiServiceProvider } from "@tests/v2/mockModules/mockHailingApiModule";
import { MockPubsubServiceProvider } from "@tests/v2/mockModules/mockPubsubModule";
import { MockUserRepository } from "@tests/v2/mockModules/mockUserModule";
import { MockCampaignServiceProvider } from "@tests/v2/mockModules/mockCampaignModule";
import { MockAppDatabaseServiceProvider } from "@tests/v2/mockModules/mockAppDatabaseModule";
import { MockHailingV2ServiceProvider } from "@tests/v2/mockModules/mockHailingV2Module";
import { MockTxRepositoryProvider } from "@tests/v2/mockModules/nockTxModule";
import { MockTxAppRepositoryProvider } from "@tests/v2/mockModules/mockTxAppModule";
import { MockLocationServiceProvider } from "@tests/v2/mockModules/mockLocationModule";
import { UtilsModule } from "@nest/modules/utils/utils.module";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { PubSubService } from "@nest/modules/pubsub/pubsub.service";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { MockLoggerServiceAdapterProvider } from "@tests/v2/mockModules/mockLoggerServiceAdapter";
import { ConfirmHailingRequestProcessor } from "@nest/modules/v2/hailing/processors/confirm-hailing-request.processor";
import { mockTxRepositoryFactory } from "@tests/v2/mockFactories/mockTxRepositoryFactory";
import { mockUserRepositoryFactory } from "@tests/v2/mockFactories/mockUserRepositoryFactory";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { mockPaymentTxRepositoryFactory } from "@tests/v2/mockFactories/mockPaymentTxRepositoryFactory";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { MockPriceRuleServiceProvider } from "@tests/v2/mockModules/mockPriceRuleModule";
import { MockCacheServiceProvider } from "@tests/v2/mockModules/mockRedisCacheModule";
import { CacheService } from "@nest/modules/cache/cache.service";

describe("ConfirmHailingRequestProcessor", () => {
  let processor: ConfirmHailingRequestProcessor;
  let txRepository: TxRepository;
  let pubsubService: PubSubService;
  let hailingApiService: HailingApiService;
  let userRepository: UserRepository;
  let paymentTxRepository: PaymentTxRepository;
  let cacheService: CacheService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [UtilsModule.forRoot()],
      providers: [
        MockLoggerServiceAdapterProvider,
        ConfirmHailingRequestProcessor,
        MockClsContextStorageServiceProvider,
        MockCreateFleetOrderDelegatee,
        MockPaymentServiceProvider,
        MockHailingApiServiceProvider,
        MockPubsubServiceProvider,
        MockUserRepository,
        MockPaymentTxRepositoryProvider,
        MockCampaignServiceProvider,
        MockHailingV2ServiceProvider,
        MockAppDatabaseServiceProvider,
        MockTxRepositoryProvider,
        MockTxAppRepositoryProvider,
        MockLocationServiceProvider,
        MockPriceRuleServiceProvider,
        MockCacheServiceProvider,
      ],
      controllers: [],
      exports: [],
    }).compile();

    processor = moduleRef.get(ConfirmHailingRequestProcessor);
    txRepository = moduleRef.get(TxRepository);
    pubsubService = moduleRef.get(PubSubService);
    hailingApiService = moduleRef.get(HailingApiService);
    userRepository = moduleRef.get(UserRepository);
    paymentTxRepository = moduleRef.get(PaymentTxRepository);
    cacheService = moduleRef.get(CacheService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const txId = "6cf2d323-8138-4a38-8230-c43a1925d40f";

  const request = {
    paymentDetail: {
      response:
        '{"app":{"appType":"SOFTPOS","appVersionCode":0,"device":{"id":"9ab42c9223ad02d4171133554b6d5aea02360364","name":"SP20001569"},"equipment":{"name":"SP20001569","os":"ANDROID"},"id":"7e1390901013b0b9df1354b29e8437307e92bc48"},"approvalCode":"094115","baseAmount":100,"batch":1,"cardData":{"aid":"A0000000031010","appName":"VISA DEBIT","applicationTransactionCounter":"0CC3","tc":"04C2A730765644B6","tsi":"6800","tvr":"8040008000"},"createById":"db36c95dc42c2b733b3ff7bfdac3a347f4f9207b","createByName":"tony.chung","createTime":"2025-07-30T09:41:15+0000","entryMode":"CHIP","ip":"**************","lastUpdateTime":"2025-07-30T09:41:16+0000","lineItem":false,"mappingId":"","messageId":"9639fbb5-c068-4193-91be-f3145520a87a","mid":"123456789012345","payment":{"adjustable":false,"baseAmount":100,"cancelable":true,"capturable":false,"createTime":"2025-07-30T09:41:14+0000","directMode":false,"lastUpdateTime":"2025-07-30T09:41:16+0000","netAmount":100,"orderId":"16341c54c70e50dddce78a9835bc3219","organize":{"address":"<EMAIL>","name":"Vis-Mobility Dev Shop"},"originTranType":"SALE","payer":"460517******6765","payerFirst":"460517","payerLast":"6765","payerName":"WING CHI CHUNG","paymentId":"982d3c3decc50854234e50233b9adf2966822b87","paymentMethod":"VISA","paymentTrace":198916,"refundable":true,"tipAmount":0},"processorAdditionalData":{},"processorReference":"250730094115","processorResult":"00","receiptOffline":false,"receiptOnline":true,"refundable":true,"requestAdditionalData":{"applicationId":"com.spectratech.softpos.vismo.demo","version":"***********-preprod"},"requireSignature":false,"settled":false,"tid":"12345678","tipAmount":0,"totalAmount":100,"trace":219992,"tranId":"92ae0195cfafe9b152589fd85743e0f79c45c78e","tranStatus":"APPROVED","tranType":"SALE"}',
    },
  };

  describe(".execute", () => {
    describe("when status is PENDING_PAYMENT should create a hailing request", () => {
      it("should create a hailing request when no exist payment tx", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository);
        mockPaymentTxRepositoryFactory.mockPaymentTxFindOne(paymentTxRepository, {}, true);
        mockPaymentTxRepositoryFactory.mockPaymentTxSave(paymentTxRepository, {});

        await expect(processor.execute(txId, request, {} as any, "123")).resolves.not.toThrow();
        expect(txRepository.save).toHaveBeenCalled();
        expect(hailingApiService.createHailingOrderV3).toHaveBeenCalled();
        expect(paymentTxRepository.save).toHaveBeenCalled();
        expect(pubsubService.publishMessageForHailingTxCreated).toHaveBeenCalled();
        expect(cacheService.del).toHaveBeenCalled();
      });

      it("should create a hailing request when exist payment tx", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository);
        mockPaymentTxRepositoryFactory.mockPaymentTxFindOne(paymentTxRepository);

        await expect(processor.execute(txId, request, {} as any, "123")).resolves.not.toThrow();
        expect(txRepository.save).toHaveBeenCalled();
        expect(hailingApiService.createHailingOrderV3).toHaveBeenCalled();
        expect(paymentTxRepository.save).not.toHaveBeenCalled();
        expect(pubsubService.publishMessageForHailingTxCreated).toHaveBeenCalled();
        expect(cacheService.del).toHaveBeenCalled();
      });

      it("if no tx found should throw an error", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository, {}, true);

        await expect(processor.execute(txId, request, {} as any, "123")).rejects.toThrow();
        expect(txRepository.save).not.toHaveBeenCalled();
        expect(hailingApiService.createHailingOrderV3).not.toHaveBeenCalled();
        expect(cacheService.del).not.toHaveBeenCalled();
      });
    });
  });
});
