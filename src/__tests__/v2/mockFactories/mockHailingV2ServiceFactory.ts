import PaymentInstrument from "@nest/modules/database/entities/paymentInstrument.entity";
import { HailingV2Service } from "@nest/modules/v2/hailing/services/hailing-v2.service";

export const mockHailingV2ServiceFactory = {
  mockGetValidPaymentInstrument: (hailingV2Service: HailingV2Service, isNull = false) => {
    jest.spyOn(hailingV2Service, "getValidPaymentInstrument").mockImplementation(() => {
      if (isNull) {
        return undefined;
      }
      return {
        id: "123",
        isPreferred: true,
        state: "VERIFIED",
        paymentGateway: "GLOBAL_PAYMENTS",
        expirationDate: new Date("2124-08-05T03:55:39.009Z"),
      } as unknown as PaymentInstrument;
    });
  },
};
