import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import sgMail from "@sendgrid/mail";

import { Log } from "@nest/decorators/log.decorator";

import LoggerServiceAdapter from "../utils/logger/logger.service";

import { ActionEmailConfig, NotificationEmailConfig } from "./types";

/**
 * Service for sending emails using SendGrid.
 */
@Injectable()
export class EmailService {
  private static readonly FROM_EMAIL = "<EMAIL>";
  private static readonly FROM_DISPLAY_NAME = `D-ASH by Vis-Mobility <${EmailService.FROM_EMAIL}>`;

  /**
   * Email templates used in SendGrid.
   * @link https://mc.sendgrid.com/dynamic-templates
   */
  private static readonly EMAIL_TEMPLATES = {
    ACTION: "d-a40c5a0d06924056a8e6fe3805572d8c",
    NOTIFICATION: "d-d2bae9b3b09a484a8643e411028c8ab6",
    OPENLINK: "d-d2bae9b3b09a484a8643e411028c8ab6",
  };

  constructor(
    private readonly configService: ConfigService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {
    sgMail.setApiKey(this.configService.getOrThrow("SENDGRID_API_KEY"));
  }

  /**
   * Sends "Action" email with action button. Use for sending emails that require user action.
   * @link https://mc.sendgrid.com/dynamic-templates
   */
  @Log()
  async sendActionEmail(config: ActionEmailConfig) {
    const sentEmail = await sgMail.send({
      ...config,
      to: config.to,
      from: EmailService.FROM_DISPLAY_NAME,
      templateId: EmailService.EMAIL_TEMPLATES.ACTION,
      dynamicTemplateData: config.dynamicTemplateData,
    });
    this.logger.info(`Action email sent to ${config.to}`);
    return sentEmail;
  }

  /**
   * Sends "Notification" email. Use for sending informational emails.
   * @link https://mc.sendgrid.com/dynamic-templates
   */
  @Log()
  async sendNotificationEmail(config: NotificationEmailConfig) {
    const sentEmail = await sgMail.send({
      ...config,
      to: config.to,
      bcc: "<EMAIL>",
      from: EmailService.FROM_DISPLAY_NAME,
      templateId: EmailService.EMAIL_TEMPLATES.NOTIFICATION,
      dynamicTemplateData: config.dynamicTemplateData,
    });
    return sentEmail;
  }

  /**
   * Sends "Open Link" email. Use for sending emails with a link to open.
   * @link https://mc.sendgrid.com/dynamic-templates
   */
  @Log()
  async sendOpenLinkEmail(config: NotificationEmailConfig) {
    const sentEmail = await sgMail.send({
      ...config,
      to: config.to,
      bcc: "<EMAIL>",
      from: EmailService.FROM_DISPLAY_NAME,
      templateId: EmailService.EMAIL_TEMPLATES.OPENLINK,
      dynamicTemplateData: config.dynamicTemplateData,
    });
    this.logger.info(`Open link email sent to ${config.to}`);
    return sentEmail;
  }
}
