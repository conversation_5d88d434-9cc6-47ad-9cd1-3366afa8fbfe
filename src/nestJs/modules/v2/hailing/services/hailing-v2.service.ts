import { Inject, Injectable } from "@nestjs/common";
import { QueryDocumentSnapshot } from "firebase-admin/firestore";
import { FirestoreEvent } from "firebase-functions/v2/firestore";

import { PaymentInstrumentState } from "@nest/modules/payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { AppUser } from "@nest/modules/user/dto/user.dto";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import Tx from "@nest/modules/database/entities/tx.entity";
import { isTxHailing, TxHailingRequest } from "@nest/modules/transaction/dto/tx.dto";
import { HailType, TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { PaymentService } from "@nest/modules/payment/payment.service";
import { LogAll } from "@nest/decorators/log-all.decorator";
import { HotelSolutionSoepayResponseDocument } from "@nest/modules/appDatabase/documents/hotelSoepayResponse.document";
import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";
import { PaymentGatewayTypes } from "@nest/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { BookingReceiptSnapshot } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";
import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { TxEventType } from "@nest/modules/transaction/dto/txEventType.dto";
import dayjs from "@nest/modules/utils/dayjs";
import { PaymentInformationType } from "@nest/modules/payment/dto/paymentInformationType.dto";

import { HailingCancellationFeeResponse } from "../dto/cancel-hail.dto";

@LogAll()
@Injectable()
export class HailingV2Service {
  constructor(
    private txRepository: TxRepository,
    private paymentService: PaymentService,
    private paymentTxRepository: PaymentTxRepository,
    private appDatabaseService: AppDatabaseService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  getValidPaymentInstrument(appUser: AppUser, paymentInstrumentId?: string) {
    const validPaymentInstrument = appUser.paymentInstruments.find((paymentInstrument) =>
      paymentInstrumentId
        ? paymentInstrument && paymentInstrument.id === paymentInstrumentId
        : paymentInstrument &&
          paymentInstrument.expirationDate > new Date() &&
          paymentInstrument.state === PaymentInstrumentState.VERIFIED &&
          paymentInstrument.isPreferred,
    );

    return validPaymentInstrument;
  }

  async updateTxToFailed(txId: string): Promise<Tx> {
    const tx = await this.txRepository.findOne({ where: { id: txId } });
    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    if (isTxHailing(tx)) {
      tx.metadata.status = TxHailingRequestStatus.FAILED;
      await this.txRepository.save(tx);
    }

    return tx;
  }

  async upsertPaymentTxWhenHotelSolutionSeopayRespnseCallback(
    event: FirestoreEvent<QueryDocumentSnapshot<HotelSolutionSoepayResponseDocument> | undefined>,
  ) {
    if (!event.data) {
      throw errorBuilder.b2bApp.noDataInsideSoepayResponse();
    }

    const { hailingTxId, response } = event.data.data() as HotelSolutionSoepayResponseDocument;

    const tx = await this.txRepository.findOne({
      where: { id: hailingTxId },
      relations: { paymentTx: true },
    });

    if (!tx) {
      throw errorBuilder.transaction.notFound(hailingTxId);
    }

    if (tx.paymentTx && tx.paymentTx.length > 0) {
      const successPaymentTx = tx.paymentTx.find((paymentTx) => paymentTx.status === "SUCCESS");
      if (successPaymentTx) {
        return successPaymentTx;
      }
    }

    const newPaymentTx = this.paymentService.extractPaymentTxInfoFromDocument(
      JSON.parse(response) as any,
      PaymentGatewayTypes.SOEPAY,
    );

    const savedPaymentTx = await this.paymentTxRepository.save({
      ...newPaymentTx,
      tx: { id: tx.id },
    } as PaymentTx);

    return savedPaymentTx;
  }

  async getCancellationFee(txId: string): Promise<HailingCancellationFeeResponse> {
    const tx = await this.txRepository.findOne({
      where: { id: txId },
      relations: ["paymentTx", "txEvents"],
    });

    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    if (!isTxHailing(tx)) {
      return { cancellationFee: 0 };
    }

    const { shouldChargeCancellationFee, isFullCancellationFee } = await this.getShouldChargeCancellationFee(tx);

    if (!shouldChargeCancellationFee) {
      return { cancellationFee: 0 };
    }

    const breakdown = await this.getCancellationFeeBreakdown(
      tx.metadata.request.platformType === "FLEET",
      isFullCancellationFee,
      tx,
      null,
      tx.findLastPaymentType(PaymentInformationType.AUTH),
    );

    return { cancellationFee: breakdown.total };
  }

  async getCancellationFeeBreakdown(
    isFleet: boolean,
    isFullCancellationFee: boolean,
    tx: Tx,
    feetBookingSnapshot: BookingReceiptSnapshot | null,
    successAuth?: PaymentTx,
  ) {
    const hailConfig = await this.appDatabaseService.configurationRepository().getHailConfig();

    const payoutAmount = isFleet ? feetBookingSnapshot?.cancellationFee ?? 0 : hailConfig.dashCancellationFee;

    const dashFee = isFleet ? 0 : 5;
    const total =
      isFleet && isFullCancellationFee ? Math.min(hailConfig.fleetCancellationFee, successAuth?.amount ?? 0) : 20;

    tx.payoutAmount = payoutAmount;
    tx.total = total;
    tx.dashFee = dashFee;

    const breakdown = {
      total: tx.total,
      driverPayout: tx.payoutAmount,
      dashFee: tx.dashFee,
    };

    return breakdown;
  }

  async getShouldChargeCancellationFee(tx: TxHailingRequest) {
    let shouldChargeCancellationFee = false;
    let isFullCancellationFee = false;

    const lastHailingMerchantAcceptsOrderEvent = tx.findLastEventType(TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER);
    const lastHailingMerchantCancelsOrderEvent = tx.findLastEventType(TxEventType.HAILING_MERCHANT_CANCELS_ORDER);

    const isScheduled = tx.metadata.type === HailType.SCHEDULED;

    const isMatched = Boolean(
      lastHailingMerchantAcceptsOrderEvent &&
        (!lastHailingMerchantCancelsOrderEvent ||
          lastHailingMerchantCancelsOrderEvent.createdAt < lastHailingMerchantAcceptsOrderEvent.createdAt),
    );
    const requestTime = dayjs(tx?.metadata?.request?.time).tz("Asia/Hong_Kong");
    const acceptedOrderTime = lastHailingMerchantAcceptsOrderEvent
      ? dayjs(lastHailingMerchantAcceptsOrderEvent.createdAt).toDate()
      : null;

    if (isScheduled) {
      this.logger.info("HailingV2Service/getShouldChargeCancellationFee-isScheduled", {
        currentTime: dayjs().tz("Asia/Hong_Kong").toDate(),
        requestTime,
        acceptedOrderTime,
        isMatched,
      });
      isFullCancellationFee = isMatched && requestTime && requestTime.diff(dayjs(), "minutes") <= 60;
      shouldChargeCancellationFee =
        isMatched && Boolean(acceptedOrderTime && dayjs().diff(acceptedOrderTime, "minutes") >= 3);
    } else {
      isFullCancellationFee = true;
      shouldChargeCancellationFee =
        isMatched && Boolean(acceptedOrderTime && dayjs().diff(acceptedOrderTime, "minutes") >= 3);
    }

    return { shouldChargeCancellationFee, isFullCancellationFee };
  }
}
