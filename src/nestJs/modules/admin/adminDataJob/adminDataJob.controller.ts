import { <PERSON>, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";

import { MeterService } from "@nest/modules/meter/meter.service";
import { apiTags } from "@nest/modules/utils/utils/swagger.utils";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminDataJobController {
  constructor(private readonly meterService: MeterService) {}

  @Post("sync-firestore-meter-to-bigquery")
  @ApiOperation({ summary: "Sync Firestore data Meters to BigQuery" })
  @ApiResponse({ status: 200, description: "Sync Firestore data Meters to BigQuery" })
  async syncFirestoreMeterToBigQuery(): Promise<string> {
    const result = await this.meterService.syncMeters();
    if (result === 0) {
      return "No meters to process.";
    }
    return `Meter sync-firestore-meter-to-bigquery endpoint is working! Processed ${result} meters.`;
  }

  @Post("sync-firestore-heartbeat-to-bigquery")
  @ApiOperation({ summary: "Sync Firestore data Heartbeats to BigQuery" })
  @ApiResponse({ status: 200, description: "Sync Firestore data Heartbeats to BigQuery" })
  async syncFirestoreHeartbeatToBigQuery(): Promise<string> {
    const result = await this.meterService.syncHeartbeats();
    if (result === 0) {
      return "No heartbeats to process.";
    }
    return `Meter sync-firestore-heartbeat-to-bigquery endpoint is working! Processed ${result} heartbeats.`;
  }
}
