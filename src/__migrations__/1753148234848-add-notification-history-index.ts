import { MigrationInterface, Query<PERSON>un<PERSON> } from "typeorm";

export class AddNotificationHistoryIndex1753148234848 implements MigrationInterface {
  name = "AddNotificationHistoryIndex1753148234848";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add index on notificationTaskId and userId columns to improve the performance of
    // the subquery in findDashMerchantByPhoneNumbersExcludingProcessed method
    await queryRunner.query(
      'CREATE INDEX "idx_notification_history_task_user" ON "notification_history" ("notificationTaskId", "userId")',
    );

    // Add index on userId alone for other queries that might filter by userId
    await queryRunner.query('CREATE INDEX "idx_notification_history_user_id" ON "notification_history" ("userId")');

    // Add index on userType for queries that filter by user type
    await queryRunner.query('CREATE INDEX "idx_notification_history_user_type" ON "notification_history" ("userType")');

    // Add index on status column in notification_task table
    await queryRunner.query('CREATE INDEX "idx_notification_task_status" ON "notification_task" ("status")');

    // Add index on scheduledAt column for time-based queries
    await queryRunner.query('CREATE INDEX "idx_notification_task_scheduled_at" ON "notification_task" ("scheduledAt")');

    // Add index on userType in notification_task table
    await queryRunner.query('CREATE INDEX "idx_notification_task_user_type" ON "notification_task" ("userType")');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop all indices if migration is reverted
    await queryRunner.query('DROP INDEX "idx_notification_history_task_user"');
    await queryRunner.query('DROP INDEX "idx_notification_history_user_id"');
    await queryRunner.query('DROP INDEX "idx_notification_history_user_type"');
    await queryRunner.query('DROP INDEX "idx_notification_task_status"');
    await queryRunner.query('DROP INDEX "idx_notification_task_scheduled_at"');
    await queryRunner.query('DROP INDEX "idx_notification_task_user_type"');
  }
}
