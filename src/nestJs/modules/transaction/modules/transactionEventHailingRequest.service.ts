import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { PlatformMerchantType } from "@nest/modules/database/entities/merchant.entity";
import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";
import { CancelFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/CancelFleetOrderDelegatee";
import { BookingReceiptSnapshot } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";
import { PlatformType } from "@nest/modules/me/modules/meHailing/dto/meHailing.dto";
import dayjs from "@nest/modules/utils/dayjs";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { ClientType } from "@nest/modules/v2/hailing/dto/create-hail.dto";
import { TxEventRepository } from "@nest/modules/database/repositories/txEvent.repository";
import { Log } from "@nest/decorators/log.decorator";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { MerchantService } from "@nest/modules/merchant/merchant.service";
import { CacheKeyType, CacheService } from "@nest/modules/cache/cache.service";

import Tx from "../../database/entities/tx.entity";
import TxEvent from "../../database/entities/txEvent.entity";
import { MerchantRepository } from "../../database/repositories/merchant.repository";
import { PaymentTxRepository } from "../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../database/repositories/tx.repository";
import { AddEventBody } from "../../me/modules/meTransaction/dto/addEvent.dto";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { PaymentService } from "../../payment/payment.service";
import { PubSubService } from "../../pubsub/pubsub.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { isTxHailing, TxHailingRequest } from "../dto/tx.dto";
import {
  TxEventType,
  isHailingUserCancelsOrderEvent,
  isHailingUserUpdatesOrderEvent,
  isHailingMerchantAcceptsOrderEvent,
  isHailingMerchantCancelsOrderEvent,
  HailingMerchantAcceptsOrderEvent,
  HailingUserCancelsOrderEvent,
  HailingUserUpdatesOrderEvent,
  HailingMerchantPickUpConfirmedOrderEvent,
  isHailingMerchantPickUpConfirmedOrderEvent,
  isHailingHailingScheduledOrderTimeoutOrderEvent,
  HailingAdminCancelsOrderEvent,
  isHailingAdminCancelsOrderEvent,
  isHailingMerchantArrivedDestinationEvent,
  HailingMerchantArrivedDestinationEvent,
  isHailingOrderCompletedEvent,
  HailingOrderCompletedEvent,
  isHailingMerchantApproachingDestinationEvent,
  HailingMerchantApproachingDestinationEvent,
} from "../dto/txEventType.dto";
import { HailType, TxHailingRequestStatus } from "../dto/txHailingRequest.dto";
import { TxTypes } from "../dto/txType.dto";
import { TransactionFactoryService } from "../transactionFactory/transactionFactory.service";

/**
 * Transaction service
 */
@Injectable()
export class TransactionEventHailingRequestService {
  constructor(
    @InjectRepository(MerchantRepository) private merchantRepository: MerchantRepository,
    private paymentService: PaymentService,
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
    private pubsubService: PubSubService,
    private readonly transactionFactoryService: TransactionFactoryService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly appDatabaseService: AppDatabaseService,
    private readonly cancelFleetOrderDelegatee: CancelFleetOrderDelegatee,
    private readonly txEventRepository: TxEventRepository,
    private readonly hailingApiService: HailingApiService,
    private readonly cacheService: CacheService,
    private readonly merchantService: MerchantService,
  ) {}

  @Log()
  async addHailingEvent(tx: TxHailingRequest, createdBy: string, addEventBody: AddEventBody): Promise<TxEvent> {
    const txEvent = TxEvent.fromAddEvent(tx, createdBy, addEventBody);
    if (!txEvent) {
      throw errorBuilder.transaction.eventTypeNotSupported(tx.id, tx.type);
    }

    let toSaveTx = tx;
    let resultTxEvent = txEvent;

    if (isHailingUserCancelsOrderEvent(txEvent)) {
      const result = await this.addHailingEventUserCancelsOrder(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    } else if (isHailingUserUpdatesOrderEvent(txEvent)) {
      const result = await this.addHailingEventUserUpdatesOrder(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    } else if (isHailingMerchantAcceptsOrderEvent(txEvent)) {
      const result = await this.addHailingEventMerchantAcceptsOrder(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    } else if (isHailingMerchantCancelsOrderEvent(txEvent)) {
      const result = await this.addHailingEventMerchantCancelsOrder(toSaveTx);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent || txEvent;
    } else if (isHailingMerchantPickUpConfirmedOrderEvent(txEvent)) {
      const result = await this.addHailingMerchantPickUpConfirmedOrderEvent(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    } else if (isHailingHailingScheduledOrderTimeoutOrderEvent(txEvent)) {
      const result = await this.addHailingScheduledOrderTimeoutOrderEvent(toSaveTx);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent || txEvent;
    } else if (isHailingAdminCancelsOrderEvent(txEvent)) {
      const result = await this.addHailingAdminCancelsOrderEvent(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    } else if (isHailingMerchantArrivedDestinationEvent(txEvent)) {
      const result = await this.addHailingMerchantArrivedDestinationEvent(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    } else if (isHailingOrderCompletedEvent(txEvent)) {
      const result = await this.addHailingOrderCompletedEvent(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    } else if (isHailingMerchantApproachingDestinationEvent(txEvent)) {
      const result = await this.addHailingMerchantApproachingDestinationEvent(toSaveTx, txEvent);
      toSaveTx = result.tx;
      resultTxEvent = result.txEvent;
    }

    await this.pubsubService.publishMessageForHailingStatusChanged({
      txId: toSaveTx.id,
      status: toSaveTx.metadata.status,
    });

    await this.txRepository.save(toSaveTx);
    await this.txEventRepository.save(resultTxEvent);

    const cacheConfig = this.cacheService.getCacheConfig(CacheKeyType.PUBLIC_HAILING_REQUEST, tx.id);
    await this.cacheService.del(cacheConfig.key);

    return resultTxEvent;
  }

  async addHailingScheduledOrderTimeoutOrderEvent(
    tx: TxHailingRequest,
  ): Promise<{ tx: TxHailingRequest; txEvent?: TxEvent }> {
    const isFleet = tx?.metadata?.platformType === PlatformType.FLEET;
    const isB2bApp = tx.metadata.clientType === ClientType.B2B_APP;

    if (isB2bApp) {
      const salePaymentTx = tx.paymentTx?.find(
        (paymentTx) =>
          paymentTx.type === PaymentInformationType.SALE && paymentTx.status === PaymentInformationStatus.SUCCESS,
      );

      if (salePaymentTx) {
        const refundPaymentTx = await this.paymentService.processRefund(salePaymentTx, "SYSTEM", salePaymentTx?.amount);
        if (!tx.paymentTx) {
          tx.paymentTx = [];
        }
        tx.paymentTx.push(refundPaymentTx);
      } else {
        this.logger.warn("transactionEventHailingRequestService/addHailingScheduledOrderTimeoutOrderEvent-end", {
          message: "No sale payment tx found",
          tx,
        });
      }
    } else {
      const nonVoidedAuthPaymentTx = tx.paymentTx?.find(
        (paymentTx) =>
          paymentTx.type === PaymentInformationType.AUTH && paymentTx.status === PaymentInformationStatus.SUCCESS,
      );

      if (nonVoidedAuthPaymentTx) {
        const voidPaymentTx = await this.paymentService.voidPayment(nonVoidedAuthPaymentTx?.id, "SYSTEM");
        if (!tx.paymentTx) {
          tx.paymentTx = [];
        }
        tx.paymentTx.push(voidPaymentTx);
      } else {
        this.logger.warn("transactionEventHailingRequestService/addHailingScheduledOrderTimeoutOrderEvent-end", {
          message: "No auth payment tx found",
          tx,
        });
      }
    }

    tx.metadata.status = TxHailingRequestStatus.TIMED_OUT;
    tx.metadata.timedOutAt = new Date();

    if (isFleet) {
      await this.cancelFleetOrderDelegatee.execute(tx.id);
    }

    await this.transactionFactoryService.resetDiscountsForHailingRequest(tx);

    return { tx };
  }

  /**
   * Pick up confirmed order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingMerchantPickUpConfirmedOrderEvent
   * @returns {tx, txEvent} TxHailingRequest and TxEvent
   */
  async addHailingMerchantPickUpConfirmedOrderEvent(
    tx: TxHailingRequest,
    txEvent: HailingMerchantPickUpConfirmedOrderEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    const meterId = txEvent.content.fleetMockMeterId ?? txEvent.content.meterId;
    this.logger.info("addHailingMerchantPickUpConfirmedOrderEvent", { tx, txEvent, meterId });
    let foundTx = await this.txRepository.findOneBy({ id: txEvent.content.txId });

    if (!foundTx) {
      this.logger.warn("Tx not found in sql, try to get from firestore and upsert", { tx, meterId });
      const tripDocument = await this.appDatabaseService.meterTripRepository(meterId).findOneById(txEvent.content.txId);
      if (!tripDocument) {
        throw errorBuilder.meter.tripNotFound(meterId, txEvent.content.txId);
      }
      const newTx = new Tx();
      newTx.id = txEvent.content.txId;
      newTx.txApp = tx.txApp;
      newTx.type = TxTypes.TRIP;
      newTx.merchant = tx.merchant;
      newTx.user = tx.user;
      newTx.metadata = tripDocument;
      if (tx.organizationId) {
        newTx.organizationId = tx.organizationId;
      }
      if (tx.metadata.isFixedPrice) {
        newTx.metadata.isFixedPrice = tx.metadata.isFixedPrice;
      }
      await this.txRepository.upsert(newTx, { conflictPaths: { id: true } });
      foundTx = newTx;
    }

    await Promise.all(
      tx.paymentTx?.map((paymentTx) =>
        this.paymentTxRepository.update(paymentTx.id, { ...paymentTx, tx: { id: txEvent.content.txId } }),
      ) ?? [],
    );
    tx.paymentTx = [];

    tx.parentTx = { id: txEvent.content.txId } as Tx;

    await this.transactionFactoryService.updateHailingTripWithDiscounts(foundTx, tx, meterId);

    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.ON_GOING;
    }

    return { tx, txEvent };
  }

  /**
   * Merchant cancels order event mutator
   * @param tx TxHailingRequest
   * @returns {tx, txEvent} TxHailingRequest and TxEvent
   */
  async addHailingEventMerchantCancelsOrder(
    tx: TxHailingRequest,
  ): Promise<{ tx: TxHailingRequest; txEvent?: TxEvent }> {
    tx.merchant = null;
    tx.payoutMerchant = null;
    if (tx?.metadata?.licensePlate) {
      tx.metadata.licensePlate = undefined;
    }

    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.PENDING;
    }

    return { tx };
  }

  /**
   * Merchant accepts order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingMerchantAcceptsOrderEvent
   * @returns {tx, txEvent} TxHailingRequest and TxEvent
   */
  async addHailingEventMerchantAcceptsOrder(
    tx: TxHailingRequest,
    txEvent: HailingMerchantAcceptsOrderEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    const platformMerchantType = txEvent.content.platformMerchantType
      ? txEvent.content.platformMerchantType
      : PlatformMerchantType.DASH;

    const merchant = await this.merchantRepository.findOne({
      where: { phoneNumber: txEvent.content.phoneNumber, platformMerchantType },
    });

    if (!merchant) {
      throw errorBuilder.transaction.merchantNotFound(txEvent.content.phoneNumber);
    }
    if (txEvent.content.fleetMockMeterId) {
      tx.payoutMerchant = await this.merchantService.getPayoutMerchantByLicensePlate(txEvent.content.fleetMockMeterId);
    } else if (txEvent.content.meter) {
      tx.payoutMerchant = await this.merchantService.getPayoutMerchantByLicensePlate(txEvent.content.meter);
    }

    if (txEvent.content.licensePlate) {
      this.logger.info("addHailingEventMerchantAcceptsOrder-addLicensePlate", {
        tx,
        txEvent,
        licensePlate: txEvent.content.licensePlate,
      });
      tx.metadata.licensePlate = txEvent.content.licensePlate;
    } else if (txEvent.content.meter) {
      tx.metadata.licensePlate = txEvent.content.meter;
    }

    tx.merchant = merchant;

    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.ACCEPTED;
    }

    await this.pubsubService.publishMessageForPickupReminder({
      txId: tx.id,
    });

    return { tx, txEvent };
  }

  async getShouldChargeCancellationFee(tx: TxHailingRequest) {
    this.logger.info("transactionEventHailingRequestService/getShouldChargeCancellationFee-start", { tx });
    let shouldChargeCancellationFee = false;
    let isFullCancellationFee = false;

    const lastHailingMerchantAcceptsOrderEvent = tx.findLastEventType(TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER);
    const lastHailingMerchantCancelsOrderEvent = tx.findLastEventType(TxEventType.HAILING_MERCHANT_CANCELS_ORDER);

    const isScheduled = tx.metadata.type === HailType.SCHEDULED;
    const isMatched = Boolean(
      lastHailingMerchantAcceptsOrderEvent &&
        (!lastHailingMerchantCancelsOrderEvent ||
          lastHailingMerchantCancelsOrderEvent.createdAt < lastHailingMerchantAcceptsOrderEvent.createdAt),
    );
    const requestTime = dayjs(tx?.metadata?.request?.time).tz("Asia/Hong_Kong");
    const acceptedOrderTime = lastHailingMerchantAcceptsOrderEvent
      ? dayjs(lastHailingMerchantAcceptsOrderEvent.createdAt).toDate()
      : null;

    if (isScheduled) {
      this.logger.info("transactionEventHailingRequestService/getShouldChargeCancellationFee-isScheduled", {
        currentTime: dayjs().tz("Asia/Hong_Kong").toDate(),
        requestTime,
        acceptedOrderTime,
        isMatched,
      });
      isFullCancellationFee = isMatched && requestTime && requestTime.diff(dayjs(), "minutes") <= 60;
      shouldChargeCancellationFee =
        isMatched && Boolean(acceptedOrderTime && dayjs().diff(acceptedOrderTime, "minutes") >= 3);
    } else {
      isFullCancellationFee = true;
      shouldChargeCancellationFee =
        isMatched && Boolean(acceptedOrderTime && dayjs().diff(acceptedOrderTime, "minutes") >= 3);
    }

    this.logger.info("transactionEventHailingRequestService/getShouldChargeCancellationFee-end", {
      shouldChargeCancellationFee,
      isFullCancellationFee,
      isScheduled,
      isMatched,
    });

    return { shouldChargeCancellationFee, isFullCancellationFee };
  }

  async getCancellationFeeBreakdown(
    isFleet: boolean,
    isFullCancellationFee: boolean,
    tx: Tx,
    feetBookingSnapshot: BookingReceiptSnapshot | null,
    successAuth?: PaymentTx,
  ) {
    if (!isTxHailing(tx)) {
      throw errorBuilder.transaction.wrongImplement(
        tx.type,
        "TransactionEventHailingRequestService/getCancellationFeeBreakdown",
      );
    }
    const hailConfig = await this.appDatabaseService.configurationRepository().getHailConfig();

    this.logger.info("transactionEventHailingRequestService/getCancellationFeeBreakdown-start", {
      isFleet,
      isFullCancellationFee,
      hailConfig,
      successAuth,
      tx,
    });

    const payoutAmount = isFleet ? feetBookingSnapshot?.cancellationFee ?? 0 : hailConfig.dashCancellationFee;

    const dashFee = isFleet ? 0 : 5;
    const total =
      isFleet && isFullCancellationFee
        ? Math.min(hailConfig.fleetCancellationFee, tx.metadata.minMaxFareCalculations?.[0].total ?? 0)
        : 20;

    tx.payoutAmount = payoutAmount;
    tx.total = total;
    tx.dashFee = dashFee;

    const breakdown = {
      total: tx.total,
      driverPayout: tx.payoutAmount,
      dashFee: tx.dashFee,
    };

    this.logger.info("transactionEventHailingRequestService/getCancellationFeeBreakdown-end", {
      isFleet,
      isFullCancellationFee,
      hailConfig,
      successAuth,
      breakdown,
    });

    return breakdown;
  }

  /**
   * User cancels order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingUserCancelsOrderEvent
   * @returns {tx, txEvent} TxHailingRequest and TxEvent
   */
  async addHailingEventUserCancelsOrder(
    tx: TxHailingRequest,
    txEvent: HailingUserCancelsOrderEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    const isFleet = tx?.metadata?.request?.platformType === PlatformType.FLEET;

    const { shouldChargeCancellationFee, isFullCancellationFee } = await this.getShouldChargeCancellationFee(tx);

    const isB2bApp = tx.metadata.clientType === ClientType.B2B_APP;

    const successPaymentTx = tx.findLastPaymentType(
      isB2bApp ? PaymentInformationType.SALE : PaymentInformationType.AUTH,
    );

    if (!successPaymentTx && tx.metadata.status !== TxHailingRequestStatus.PENDING_PAYMENT) {
      throw errorBuilder.transaction.successAuthNotFound(tx.id);
    }

    let feetBookingSnapshot: BookingReceiptSnapshot | null = null;

    if (isFleet) {
      feetBookingSnapshot = await this.cancelFleetOrderDelegatee.execute(tx.id);
      tx.payoutAmount = feetBookingSnapshot?.cancellationFee ?? 0;
    }

    if (shouldChargeCancellationFee) {
      const breakdown = await this.getCancellationFeeBreakdown(
        isFleet,
        isFullCancellationFee,
        tx,
        feetBookingSnapshot,
        successPaymentTx,
      );

      if (!txEvent.content.charges) txEvent.content.charges = { cancellationFee: breakdown.total };
      if (!tx.metadata.charges) tx.metadata.charges = { cancellationFee: breakdown.total };

      // Backward compatible: existing cancellationFee field
      txEvent.content.charges.cancellationFee = breakdown.total;
      tx.metadata.charges.cancellationFee = breakdown.total;

      // New: detailed breakdown (only when charging)
      txEvent.content.charges.cancellationFeeBreakdown = breakdown;
      tx.metadata.charges.cancellationFeeBreakdown = breakdown;

      if (successPaymentTx?.type === PaymentInformationType.AUTH) {
        const capturePaymentTx = await this.paymentService.processCapture(successPaymentTx, breakdown.total);

        if (!tx.paymentTx) {
          tx.paymentTx = [];
        }
        tx.paymentTx.push(capturePaymentTx);

        if (!capturePaymentTx || capturePaymentTx.status === PaymentInformationStatus.FAILURE) {
          // Reset to simple structure on failure (no breakdown needed for $0)
          txEvent.content.charges.cancellationFee = 0;
          tx.metadata.charges.cancellationFee = 0;
          delete txEvent.content.charges.cancellationFeeBreakdown;
          delete tx.metadata.charges.cancellationFeeBreakdown;

          if (!tx.txEvents) {
            tx.txEvents = [];
          }
          tx.txEvents.push(txEvent);

          await this.txRepository.save(tx);

          throw errorBuilder.payment.captureFailed(capturePaymentTx.id, capturePaymentTx.gatewayResponse);
        }
      } else if (successPaymentTx?.type === PaymentInformationType.SALE && successPaymentTx.amount) {
        const refundAmount = successPaymentTx.amount - breakdown.total;
        const refundPaymentTx = await this.paymentService.processRefund(successPaymentTx, "SYSTEM", refundAmount);
        if (!tx.paymentTx) {
          tx.paymentTx = [];
        }
        tx.paymentTx.push(refundPaymentTx);

        if (!refundPaymentTx || refundPaymentTx.status === PaymentInformationStatus.FAILURE) {
          txEvent.content.charges.cancellationFee = 0;
          tx.metadata.charges.cancellationFee = 0;
          delete txEvent.content.charges.cancellationFeeBreakdown;
          delete tx.metadata.charges.cancellationFeeBreakdown;

          if (!tx.txEvents) {
            tx.txEvents = [];
          }
          tx.txEvents.push(txEvent);

          await this.txRepository.save(tx);

          throw errorBuilder.payment.refundFailed(refundPaymentTx.id, refundPaymentTx.gatewayResponse);
        }
      }

      await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
        txId: tx.id,
      });
    } else if (successPaymentTx?.type === PaymentInformationType.AUTH) {
      const voidPaymentTx = await this.paymentService.voidPayment(successPaymentTx.id, "SYSTEM");
      this.paymentTxRepository.save(voidPaymentTx);
      tx.paymentTx?.push(voidPaymentTx);
    } else if (successPaymentTx?.type === PaymentInformationType.SALE) {
      const refundPaymentTx = await this.paymentService.processRefund(
        successPaymentTx,
        "SYSTEM",
        successPaymentTx?.amount,
      );
      this.paymentTxRepository.save(refundPaymentTx);
      tx.paymentTx?.push(refundPaymentTx);
    }

    tx.metadata.status = TxHailingRequestStatus.CANCELLED;
    tx.metadata.cancelledAt = new Date();

    await this.transactionFactoryService.resetDiscountsForHailingRequest(tx);

    return { tx, txEvent };
  }

  /**
   * User updates order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingUserUpdatesOrderEvent
   * @returns {tx, txEvent} TxHailingRequest and TxEvent
   */
  async addHailingEventUserUpdatesOrder(
    tx: TxHailingRequest,
    txEvent: HailingUserUpdatesOrderEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    // Update the entire metadata with the new order data, similar to create order
    tx.metadata = {
      ...tx.metadata,
      ...txEvent.content,
    };

    return { tx, txEvent };
  }

  async addHailingAdminCancelsOrderEvent(
    tx: TxHailingRequest,
    txEvent: HailingAdminCancelsOrderEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    const isFleet = tx?.metadata?.request?.platformType === PlatformType.FLEET;

    const { shouldChargeCancellationFee, isFullCancellationFee } = await this.getShouldChargeCancellationFee(tx);

    const successAuth = tx.findLastPaymentType(PaymentInformationType.AUTH);

    if (!successAuth) {
      throw errorBuilder.transaction.successAuthNotFound(tx.id);
    }

    let feetBookingSnapshot: BookingReceiptSnapshot | null = null;

    if (isFleet) {
      feetBookingSnapshot = await this.cancelFleetOrderDelegatee.execute(tx.id, true);
      tx.payoutAmount = feetBookingSnapshot?.cancellationFee ?? 0;
    }

    if (shouldChargeCancellationFee) {
      const breakdown = await this.getCancellationFeeBreakdown(
        isFleet,
        isFullCancellationFee,
        tx,
        feetBookingSnapshot,
        successAuth,
      );

      if (!txEvent.content.charges) txEvent.content.charges = { cancellationFee: breakdown.total };
      if (!tx.metadata.charges) tx.metadata.charges = { cancellationFee: breakdown.total };

      txEvent.content.charges.cancellationFee = breakdown.total;
      tx.metadata.charges.cancellationFee = breakdown.total;

      txEvent.content.charges.cancellationFeeBreakdown = breakdown;
      tx.metadata.charges.cancellationFeeBreakdown = breakdown;

      const capturePaymentTx = await this.paymentService.processCapture(successAuth, breakdown.total);

      if (!tx.paymentTx) {
        tx.paymentTx = [];
      }
      tx.paymentTx.push(capturePaymentTx);

      if (!capturePaymentTx || capturePaymentTx.status === PaymentInformationStatus.FAILURE) {
        // Reset to simple structure on failure (no breakdown needed for $0)
        txEvent.content.charges.cancellationFee = 0;
        tx.metadata.charges.cancellationFee = 0;
        delete txEvent.content.charges.cancellationFeeBreakdown;
        delete tx.metadata.charges.cancellationFeeBreakdown;

        if (!tx.txEvents) {
          tx.txEvents = [];
        }
        tx.txEvents.push(txEvent);

        await this.txRepository.save(tx);
        const error = errorBuilder.payment.captureFailed(capturePaymentTx.id, capturePaymentTx.gatewayResponse);

        this.logger.error(
          "transactionEventHailingRequest/addHailingAdminCancelsOrderEvent-end",
          { txId: tx.id, capturePaymentTx },
          error,
        );
      }

      await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
        txId: tx.id,
      });
    } else {
      const voidPaymentTx = await this.paymentService.voidPayment(successAuth.id, "SYSTEM");
      this.paymentTxRepository.save(voidPaymentTx);
      tx.paymentTx?.push(voidPaymentTx);
    }

    tx.metadata.status = TxHailingRequestStatus.CANCELLED;
    tx.metadata.cancelledAt = new Date();

    await this.transactionFactoryService.resetDiscountsForHailingRequest(tx);

    if (!isFleet) {
      await this.hailingApiService.cancelHailingRequestByAdmin(
        tx.id,
        tx.metadata?.charges?.cancellationFee || 0,
        tx.metadata?.charges?.cancellationFeeBreakdown,
      );
    }

    return { tx, txEvent };
  }

  async addHailingMerchantArrivedDestinationEvent(
    tx: TxHailingRequest,
    _txEvent: HailingMerchantArrivedDestinationEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.ARRIVED;
    }

    return { tx, txEvent: _txEvent };
  }

  async addHailingOrderCompletedEvent(
    tx: TxHailingRequest,
    _txEvent: HailingOrderCompletedEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.COMPLETED;
      tx.metadata.completedAt = new Date();
    }

    return { tx, txEvent: _txEvent };
  }

  async addHailingMerchantApproachingDestinationEvent(
    tx: TxHailingRequest,
    _txEvent: HailingMerchantApproachingDestinationEvent,
  ): Promise<{ tx: TxHailingRequest; txEvent: TxEvent }> {
    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.APPROACHING;
    }

    return { tx, txEvent: _txEvent };
  }
}
