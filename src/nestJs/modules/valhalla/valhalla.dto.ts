import { ApiProperty } from "@nestjs/swagger";

export class ValhallaLocation {
    @ApiProperty()
    lat: number;

    @ApiProperty()
    lon: number;
}

export class ValhallaDirectionsOptions {
    @ApiProperty()
    units? = "kilometers";
}

export class ValhallaProxyRouteRequestBody {
    @ApiProperty()
    locations: ValhallaLocation[];

    @ApiProperty()
    costing? = "auto";

    @ApiProperty()
    directions_options?: ValhallaDirectionsOptions = new ValhallaDirectionsOptions();
}
