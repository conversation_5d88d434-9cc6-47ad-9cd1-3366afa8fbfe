import { LocationService } from "@nest/modules/location/location.service";

const mockLocationService = {
  getMultiplePlaceDetails: jest.fn().mockResolvedValue([]),
  getPickupPointPolygon: jest.fn().mockResolvedValue(undefined),
  reverseGeocodeV2: jest.fn().mockResolvedValue({
    place: {
      placeId: "place-id",
      formattedAddress: "formatted-address",
      displayName: "display-name",
    },
    pickupPointPolygon: undefined,
  }),
  computeRoutes: jest.fn().mockResolvedValue({
    distanceMeters: 1000,
    durationSeconds: 1000,
    encodedPolyline: "encoded-polyline",
    originPlaceDetails: {
      placeId: "place-id",
      formattedAddress: "formatted-address",
      displayName: "display-name",
      lat: 1,
      lng: 1,
    },
    destinationPlaceDetails: {
      placeId: "place-id",
      formattedAddress: "formatted-address",
      displayName: "display-name",
      lat: 1,
      lng: 1,
    },
  }),
  getPlaceDetails: jest.fn().mockResolvedValue({
    placeId: "place-id",
    formattedAddress: "formatted-address",
    displayName: "display-name",
    lat: 1,
    lng: 1,
  }),
}

export const MockLocationServiceProvider = {
  provide: LocationService,
  useValue: mockLocationService,
};
