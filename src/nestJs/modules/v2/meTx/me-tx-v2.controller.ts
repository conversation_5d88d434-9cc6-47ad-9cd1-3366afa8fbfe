import { Controller, Get, Query, Req } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiBadRequestResponse, ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Request } from "express";
import <PERSON><PERSON> from "joi";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "@nest/modules/utils/utils/swagger.utils";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { GetTransactionsResponse } from "./dto/me-tx.dto";
import { MeTxV2Service } from "./services/me-tx-v2.service";

@Controller("v2/me/transactions")
@ApiBearerAuth()
@ApiTags(...apiTags.v2_me_hailing)
export class MeTxV2Controller {
  constructor(private readonly meTxV2Service: MeTxV2Service) {}

  @Get("/")
  @ApiOperation({
    summary: "Creates an order",
  })
  @ApiResponse({ status: 201, description: "Creates an order" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.reverseGeocode.invalidParams(),
      errorBuilder.location.reverseGeocode.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async getMeTxs(
    @Req() req: Request,
    @Query("limit", new JoiValidationPipe(Joi.number().min(10).max(200).optional())) limit: number = 50,
  ): Promise<GetTransactionsResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    return this.meTxV2Service.getMeTxs(user.uid, limit);
  }

  @Get("/history")
  @ApiOperation({
    summary: "Creates an order",
  })
  @ApiResponse({ status: 201, description: "Creates an order" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.reverseGeocode.invalidParams(),
      errorBuilder.location.reverseGeocode.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async getMeTxHistories(
    @Req() req: Request,
    @Query("page") page: number = 1,
    @Query("limit", new JoiValidationPipe(Joi.number().min(10).max(200).optional())) limit: number = 50,
  ): Promise<GetTransactionsResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    return this.meTxV2Service.getMeTxHistories(user.uid, page, limit);
  }
}
