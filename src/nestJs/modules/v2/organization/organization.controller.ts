import { <PERSON>, <PERSON>, Param, Req, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiParam, ApiResponse, ApiTags, Api<PERSON>earerAuth } from "@nestjs/swagger";
import { Request } from "express";

import { AuthGuard } from "@nest/infrastructure/guards/auth.guard";

import { errorBuilder } from "../../utils/utils/error.utils";

import { OrganizationService } from "./services/organization.service";
import { OrganizationResponseDto } from "./organization.dto";

@ApiTags("Organizations")
@Controller("v2/organizations")
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get("/:id")
  @ApiOperation({ summary: "Get organization by ID" })
  @ApiParam({ name: "id", description: "Organization ID", type: "string" })
  @ApiResponse({ 
    status: 200, 
    description: "Organization found", 
    type: OrganizationResponseDto 
  })
  @ApiResponse({ status: 404, description: "Organization not found" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  async getOrganizationById(
    @Param("id") id: string,
    @Req() req: Request
  ): Promise<OrganizationResponseDto> {
    if (!req.user) {
      throw errorBuilder.auth.tokenInvalidRole();
    }
    return this.organizationService.getOrganizationById(id, req.user);
  }
}
