import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTxUserMerchantIdx1754966943012 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('CREATE INDEX CONCURRENTLY "idx_tx_userId" ON "tx" ("userId")');
        await queryRunner.query('CREATE INDEX CONCURRENTLY "idx_tx_merchantId" ON "tx" ("merchantId")');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX "idx_tx_userId"');
        await queryRunner.query('DROP INDEX "idx_tx_merchantId"');
    }

    public get transaction(): boolean {
        return false;
    }
}
