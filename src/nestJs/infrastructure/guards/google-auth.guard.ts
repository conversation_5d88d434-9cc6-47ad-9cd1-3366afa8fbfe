import {
    Injectable,
    CanActivate,
    ExecutionContext,
    UnauthorizedException,
    Inject,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { OAuth2Client } from "google-auth-library";

import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";


@Injectable()
export class GoogleAuthGuard implements CanActivate {
    constructor(
        private readonly configService: ConfigService,
        private readonly googleClient: OAuth2Client,
        @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers["authorization"] || request.headers["Authorization"];

        if (!authHeader) {
            throw new UnauthorizedException("Missing Authorization header.");
        }

        const [scheme, token] = authHeader.split(" ");

        if (scheme.toLowerCase() !== "bearer" || !token) {
            throw new UnauthorizedException(
                "Invalid header format. Expected 'Bearer <token>'.",
            );
        }

        try {
            const ticket = await this.googleClient.verifyIdToken({
                idToken: token,
                audience: this.audience,
            });

            const payload = ticket.getPayload();

            if (!payload) {
                throw new Error("Token verification failed to produce a payload.");
            }

            request["internalIdentity"] = payload;
            return true;
        } catch (error: any) {
            this.logger.error(`Internal token validation failed: ${error.message}`);
            throw new UnauthorizedException("Invalid internal service token.");
        }
    }

    private get audience(): string {
        return this.configService.get<string>("GOOGLE_CLIENT_ID", "BACKEND_FUNCTIONS");

    }
}
