# Organization Transactions Module

This module provides an API endpoint to list all transactions for an organization, filtered by transaction type (TRIP or HAILING_REQUEST) with pagination support.

## Features

- ✅ Filter transactions by type (TRIP or HAILING_REQUEST)
- ✅ Pagination with configurable limit (max 50 per page)
- ✅ Sort by createdAt in descending order (newest first)
- ✅ Include related data (user, merchant, payment information)
- ✅ TypeScript support with proper typing
- ✅ Swagger API documentation

## API Endpoint

```
GET /v2/organizations/:organizationId/transactions
```

### Path Parameters

- `organizationId` (string, required): The ID of the organization

### Query Parameters

- `type` (optional): Filter by transaction type (`TRIP` or `HAILING_REQUEST`)
  - If not provided, returns both TRIP and HAILING_REQUEST transactions
- `page` (optional, default: 1): Page number for pagination
- `limit` (optional, default: 50, max: 50): Number of items per page

### Response

```typescript
{
  items: Tx[];           // Array of transaction objects
  total: number;         // Total number of transactions
  page: number;          // Current page number
  limit: number;         // Items per page
  totalPages: number;    // Total number of pages
}
```

## Example Usage

### Get all transactions for an organization
```bash
GET /v2/organizations/123e4567-e89b-12d3-a456-426614174000/transactions
```

### Get only TRIP transactions with pagination
```bash
GET /v2/organizations/123e4567-e89b-12d3-a456-426614174000/transactions?type=TRIP&page=1&limit=20
```

### Get only HAILING_REQUEST transactions
```bash
GET /v2/organizations/123e4567-e89b-12d3-a456-426614174000/transactions?type=HAILING_REQUEST
```

## Implementation Details

### Files Structure

```
src/nestJs/modules/v2/organization-tx/
├── dto/
│   └── organization-tx.dto.ts      # Request/response DTOs and validation schemas
├── organization-tx.controller.ts   # REST API controller
├── organization-tx.service.ts      # Business logic service
├── organization-tx.module.ts       # NestJS module configuration
└── index.ts                        # Module exports
```

### Database Query

The service uses TypeORM query builder to:
1. Join with related entities (user, merchant, paymentTx)
2. Filter by organizationId
3. Filter by transaction type (if specified)
4. Sort by createdAt DESC
5. Apply pagination (skip/take)

### Authentication & Authorization

- Requires Bearer token authentication
- Uses admin API tags (intended for admin/organization management use)

## Integration

To use this module in your application:

1. Import the module:
```typescript
import { OrganizationTxModule } from '@nest/modules/v2/organization-tx';

@Module({
  imports: [OrganizationTxModule],
  // ...
})
export class AppModule {}
```

2. The endpoint will be automatically available at the specified route.

## Notes

- Maximum limit is 50 transactions per page to prevent performance issues
- Transactions are sorted by creation date (newest first)
- Only TRIP and HAILING_REQUEST transaction types are returned by default
- The organization relationship in the Tx entity is used for filtering
