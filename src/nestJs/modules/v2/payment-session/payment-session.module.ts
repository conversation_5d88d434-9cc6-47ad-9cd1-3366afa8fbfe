import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";

import { JwtModule } from "@nest/modules/jwt/jwt.module";
import { PublicHailingV2Module } from "@nest/modules/v2/public-hailing/public-hailing-v2.module";
import { SecretsModule } from "@nest/modules/secrets/secrets.module";
import { PublicHailingV2Service } from "@nest/modules/v2/public-hailing/services/public-hailing-v2.service";
import { JwtService } from "@nest/modules/jwt/jwt.service";
import { SecretsService } from "@nest/modules/secrets/secrets.service";

import { PaymentSessionService } from "./payment-session.service";
import { PaymentSessionController } from "./payment-session.controller";

@Module({
  imports: [SecretsModule, PublicHailingV2Module, ConfigModule, JwtModule],
  controllers: [PaymentSessionController],
  providers: [
    {
      provide: PaymentSessionService,
      useFactory: PaymentSessionService.loadConfigs,
      inject: [SecretsService, ConfigService, PublicHailingV2Service, JwtService],
    },
  ],
  exports: [PaymentSessionService],
})
export class PaymentSessionModule {}
