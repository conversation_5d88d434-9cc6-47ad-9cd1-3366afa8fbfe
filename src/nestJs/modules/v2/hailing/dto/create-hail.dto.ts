import { ApiProperty } from "@nestjs/swagger";
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinDate,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";

import { VehicleClass } from "@nest/modules/fleet/dto/fleet.dto";
import { LocalizedLanguage } from "@nest/modules/location/dto/location.dto";
import { PartnerKey } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";

export class HailingGuestMetaData {
  @IsOptional()
  name: string;

  @IsOptional()
  phoneNumber: string;

  @IsOptional()
  roomNumber: string;
}

export class HailingRequestMetaData {
  @ApiProperty({
    required: false,
    description: "Guest metadata",
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => HailingGuestMetaData)
  guest?: HailingGuestMetaData;
}

// Equivalent of MeterOperatingArea enum
export enum MeterOperatingArea {
  NT = "NT",
  URBAN = "URBAN",
  LANTAU = "LANTAU",
}

export class HailingCreateOrderOptionsBody {
  @ApiProperty({ example: true, description: "Is assistant required" })
  @IsBoolean()
  isAssistant: boolean;

  @ApiProperty({ example: true, description: "Is pet friendly" })
  @IsBoolean()
  isPetFriendly: boolean;
}

export enum PlatformType {
  FLEET = "FLEET",
  DASH = "DASH",
}

export enum ClientType {
  DASH = "DASH",
  HOTEL_SOLUTION = "HOTEL_SOLUTION", // Will be renamed to B2B_APP
  B2B_APP = "B2B_APP",
}

export class HalingCreateOrderItineraryStepRequest {
  @ApiProperty()
  @IsString()
  placeId: string;

  @ApiProperty()
  @IsString()
  displayName: string;
  
  @ApiProperty()
  @IsString()
  formattedAddress: string;
}

/**
 * Hailing create order
 */
export class HailingCreateOrderBodyV3 {

  @ApiProperty({ example: "6cf2d323-8138-4a38-8230-c43a1925d40f", description: "Quote identifier" })
  @IsString()
  quoteId: string;

  @ApiProperty({ required: false, example: "sessionToken", description: "Session token" })
  @IsOptional()
  @IsString()
  sessionToken?: string;

  @ApiProperty({ example: "en", description: "Languages accepted" })
  @IsString()
  language: LocalizedLanguage;

  @ApiProperty({
    example: [
      {
        placeId: "ChIJE-FWMvMDBDQRBCxQzoVA-Ao",
        displayName: "TKO Plaza",
        formattedAddress: "Tseung Kwan O Plaza Club House, 1 Tong Tak St, Tseung Kwan O",
      },
      {
        placeId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
        displayName: "Hysan Place",
        formattedAddress: "Hysan Place, 500 Hennessy Rd, Causeway Bay",
      },
    ],
    description: "Index of the location (0: origin - 1: destination)",
    type: Array<HalingCreateOrderItineraryStepRequest>,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HalingCreateOrderItineraryStepRequest)
  itinerary: HalingCreateOrderItineraryStepRequest[];

  @ApiProperty({
    required: true,
    example: PlatformType.DASH,
    description: "Type of hailing",
  })
  @IsEnum(PlatformType)
  platformType: PlatformType;

  @ApiProperty({
    required: true,
    example: ClientType.DASH,
    description: "App origin",
  })
  @IsEnum(ClientType)
  clientType: ClientType;

  @ApiProperty({
    required: false,
    example: new Date(),
    description:
      "Add time for hailing booking. If not specified, it'll create an order for the current time. It has to be at least 50 minutes in the future.",
    type: Date,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @MinDate(new Date(Date.now() + 20 * 60 * 1000)) // At least 20 minutes in the future
  time?: Date;

  @ApiProperty({
    required: false,
    example: "URadOsz9sqDON7Me9DmC",
    description: "Fleet vehicle type",
  })
  @IsOptional()
  @IsString()
  fleetVehicleType?: string;

  @ApiProperty({
    required: false,
    example: "b584cd9a-93b6-474e-9ba3-2318ed40154c",
    description: "Fleet Quote Vehicle id",
  })
  @IsOptional()
  @IsString()
  fleetQuoteVehicleId?: string;

  @ApiProperty({
    required: false,
    example: PartnerKey.SYNCAB,
    description: "Partner key",
  })
  @IsEnum(PartnerKey)
  @IsOptional()
  fleetPartnerKey?: PartnerKey;

  @ApiProperty({
    required: false,
    example: "b584cd9a-93b6-474e-9ba3-2318ed40154c",
    description: "Payment instrument id. If no specified, default payment instrument will be used",
  })
  @IsOptional()
  @IsString()
  paymentInstrumentId?: string;

  @ApiProperty({
    example: { isAssistant: true, isPetFriendly: true },
    description: "Options for the order",
  })
  @ValidateNested()
  @Type(() => HailingCreateOrderOptionsBody)
  options: HailingCreateOrderOptionsBody;

  @ApiProperty({
    required: false,
    example: true,
    description: "Prioritize favorite drivers",
  })
  @IsOptional()
  @IsBoolean()
  prioritizeFavoriteDrivers?: boolean;

  @ApiProperty({
    required: false,
    example: "6cf2d323-8138-4a38-8230-c43a1925d40f",
    description: "Id of third party campaign to use",
  })
  @IsOptional()
  @IsString()
  campaignIdThirdParty?: string;

  @ApiProperty({
    required: false,
    example: "e222f64c-75e6-4c04-9e62-8b13f45d8719",
    description: "Id of dash campaign to use",
  })
  @IsOptional()
  @IsString()
  campaignIdDash?: string;

  @ApiProperty({
    required: false,
    example: "some jsonLogic rule",
    description: "Rules of third party campaign to use",
  })
  @IsOptional()
  @IsString()
  campaignRulesThirdParty?: string;

  @ApiProperty({
    required: false,
    example: "some jsonLogic rule",
    description: "Rules of dash campaign to use",
  })
  @IsOptional()
  @IsString()
  campaignRulesDash?: string;

  @ApiProperty({
    required: false,
    example: true,
    description: "Whether to use double tunnel fee",
  })
  @IsOptional()
  @IsBoolean()
  isDoubleTunnelFee?: boolean;

  @ApiProperty({
    required: false,
    example: ["FOUR_SEATER", "LUXURY"],
    description: "Preference vehicle classes",
  })
  @IsOptional()
  @IsArray()
  @IsEnum(VehicleClass, { each: true })
  preferVehicleClasses?: VehicleClass[];

  @IsOptional()
  @ValidateNested()
  @Type(() => HailingRequestMetaData)
  metadata?: HailingRequestMetaData;

  @IsOptional()
  @IsArray()
  @IsEnum(MeterOperatingArea, { each: true })
  operatingAreas?: MeterOperatingArea[];
}

export class HailingGetQuoteBodyV3 {
  @ApiProperty({ example: "en", description: "Languages accepted" })
  @IsString()
  language: LocalizedLanguage;

  @ApiProperty({ example: ["ChIJE-FWMvMDBDQRBCxQzoVA-Ao", "ChIJj6ZHpA4DBDQRD99EOoiAGwo"], description: "Place ids" })
  @IsArray()
  @IsString({ each: true })
  placeIds: string[];

  @ApiProperty({
    required: true,
    example: PlatformType.DASH,
    description: "Type of hailing",
  })
  @IsEnum(PlatformType)
  platformType: PlatformType;

  @ApiProperty({
    required: true,
    example: ClientType.DASH,
    description: "App origin",
  })
  @IsEnum(ClientType)
  clientType: ClientType;

  @ApiProperty({
    required: false,
    example: new Date(),
    description:
      "Add time for hailing booking. If not specified, it'll create an order for the current time. It has to be at least 50 minutes in the future.",
    type: Date,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  time?: Date;

  @ApiProperty({
    required: false,
    example: "MPT",
    description: "Fleet vehicle type",
  })
  @IsOptional()
  @IsString()
  fleetVehicleTypeKey?: string;

  @ApiProperty({
    required: false,
    example: PartnerKey.SYNCAB,
    description: "Partner key",
  })
  @IsEnum(PartnerKey)
  @IsOptional()
  fleetPartnerKey?: PartnerKey;

  @ApiProperty({
    required: false,
    example: true,
    description: "Whether to use double tunnel fee",
  })
  @IsOptional()
  @IsBoolean()
  isDoubleTunnelFee?: boolean;

  @ApiProperty({
    required: false,
    example: "b584cd9a-93b6-474e-9ba3-2318ed40154c",
    description: "Payment instrument id. If no specified, default payment instrument will be used",
  })
  @IsOptional()
  @IsString()
  paymentInstrumentId?: string;

  @ApiProperty({
    required: false,
    example: ["FOUR_SEATER", "LUXURY"],
    description: "Preference vehicle classes",
  })
  @IsOptional()
  @IsArray()
  @IsEnum(VehicleClass, { each: true })
  preferVehicleClasses?: VehicleClass[];

  @ApiProperty({
    required: false,
    description: "Metadata",
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => HailingRequestMetaData)
  metadata?: HailingRequestMetaData;
}

// Equivalent of DashTransactionFees
export class DashTransactionFees {
  @ApiProperty({ description: "Dash fee constant amount", example: 5 })
  @IsNumber()
  dashFeeConstant: number;

  @ApiProperty({ description: "Dash fee rate", example: 0.05 })
  @IsNumber()
  dashFeeRate: number;
}

export class FareCalculation {
  @ApiProperty({ description: "Estimated fare fee", example: 100 })
  @IsNumber()
  estimatedFareFee: number;

  @ApiProperty({ description: "Estimated tunnel fee", example: 20 })
  @IsNumber()
  estimatedTunnelFee: number;

  @ApiProperty({ description: "Fleet booking fee", example: 10 })
  @IsNumber()
  fleetBookingFee: number;

  @ApiProperty({ description: "Additional booking fee", example: 5 })
  @IsNumber()
  additionalBookingFee: number;

  @ApiProperty({ description: "Dash booking fee", example: 15 })
  @IsNumber()
  dashBookingFee: number;

  @ApiProperty({ description: "Boost amount", example: 0 })
  @IsNumber()
  boostAmount: number;

  @ApiProperty({ description: "Subtotal amount", example: 150 })
  @IsNumber()
  subTotal: number;

  @ApiProperty({ description: "Dash transaction fee", example: 5 })
  @IsNumber()
  dashTransactionFee: number;

  @ApiProperty({ description: "Total amount", example: 155 })
  @IsNumber()
  total: number;

  @ApiProperty({ description: "Discount amount", example: 10 })
  @IsNumber()
  discount: number;

  @ApiProperty({ description: "Discounted total amount", example: 145 })
  @IsNumber()
  discountedTotal: number;

  @ApiProperty({ description: "Transaction fees details" })
  @ValidateNested()
  @Type(() => DashTransactionFees)
  transactionFees: DashTransactionFees;
}

export class HailQuoteMetadata {
  @ApiProperty({ description: "Number of seats", example: 4 })
  @IsNumber()
  seatsCount: number;

  @ApiProperty({ description: "Number of luggage spaces", example: 2 })
  @IsNumber()
  luggageCount: number;
}
export class HailQuote {
  @ApiProperty({ description: "Price matrix calculation" })
  @ValidateNested()
  @Type(() => FareCalculation)
  priceMatrix: FareCalculation;

  @ApiProperty({ description: "Vehicle class identifier", example: "FOUR_SEATER" })
  @IsString()
  vehicleClass: string;

  @ApiProperty({ description: "Vehicle class display name", example: "4-Seater" })
  @IsString()
  vehicleClassName: string;

  @ApiProperty({ description: "Vehicle class description", example: "Standard 4-seater taxi" })
  @IsString()
  vehicleClassDescription: string;

  @ApiProperty({ description: "URL to vehicle icon", example: "https://example.com/icons/sedan.png", required: false })
  @IsOptional()
  @IsString()
  vehicleIconUrl?: string;

  @ApiProperty({ description: "URL to fleet icon", example: "https://example.com/icons/fleet.png", required: false })
  @IsOptional()
  @IsString()
  fleetIconUrl?: string;

  @ApiProperty({ description: "Quote metadata" })
  @ValidateNested()
  @Type(() => HailQuoteMetadata)
  metadata: HailQuoteMetadata;

  @ApiProperty({ description: "Operating areas", example: ["URBAN", "NT"] })
  @IsArray()
  @IsEnum(MeterOperatingArea, { each: true })
  operatingArea: MeterOperatingArea[];
}

export class CreateHailQuoteResponse {
  @ApiProperty({ description: "Quote identifier", example: "6cf2d323-8138-4a38-8230-c43a1925d40f" })
  @IsString()
  quoteId: string;

  @ApiProperty({ description: "List of quotes", type: [HailQuote] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HailQuote)
  quotes: HailQuote[];
}

export class ConfirmHailingRequestPaymentDetail {
  @ApiProperty({ description: "Payment body", example: "payment body" })
  @IsString()
  response: string;
}
export class ConfirmHailingRequestBody {
  @ApiProperty({ description: "Payment details" })
  @ValidateNested()
  @Type(() => ConfirmHailingRequestPaymentDetail)
  paymentDetail: ConfirmHailingRequestPaymentDetail;
}
