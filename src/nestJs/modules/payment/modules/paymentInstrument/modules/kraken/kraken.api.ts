import { Injectable } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";

import { errorBuilder, isKrakenError } from "@nest/modules/utils/utils/error.utils";
import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";

import { CaptureBody } from "./dto/capture.dto";
import { KrakenCardPublic } from "./dto/card.dto";
import { DeleteCardParams } from "./dto/deleteCard.dto";
import { PaymentBody, PaymentConsumerAuthenticationBody } from "./dto/payment.dto";
import { RefundBody } from "./dto/refund.dto";
import { SaveCardBody } from "./dto/saveCard.dto";
import { KrakenTransactionPublic } from "./dto/transaction.dto";
import { UpdateCardBody, UpdateCardParams } from "./dto/updateCard.dto";
import { ValidateAuthenticationResultsBody } from "./dto/validateAuthenticationResults.dto";
import { KrakenTokenizedCardRequestDto } from "./dto/digitalWallet.dto";


@Injectable()
export class KrakenApi {
  /**
   * Creates an instance of KrakenApi.
   * Should only be used in krakenservice.
   * @param configService - Provides application configuration.
   */
  constructor(private readonly httpService: HttpService, private clsService: ClsContextStorageService) {}

  /**
   * Handles errors from Kraken.
   * Should only be used in krakenservice.
   * @param error - Error object from the HTTP request.
   * @throws An error formatted by errorBuilder.kraken.
   */
  private krakenErrorHandler(error: any): never {
    if (isKrakenError(error)) {
      throw errorBuilder.kraken.api(error);
    }
    throw errorBuilder.kraken.unexpected(error);
  }

  private async _get<T>(uri: string): Promise<T> {
    try {
      const response = await firstValueFrom(
        this.httpService.get<T>(`${uri}`, {
          headers: {
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );

      return response.data;
    } catch (error) {
      this.krakenErrorHandler(error);
    }
  }

  private async _post<T>(uri: string, body: any): Promise<T> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<T>(`${uri}`, body, {
          headers: {
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );

      return response.data;
    } catch (error) {
      this.krakenErrorHandler(error);
    }
  }

  /**
   * Saves a new card to the Kraken API.
   * Should only be used in krakenservice.
   * @param card - The card details to save.
   * @returns The saved card details.
   */
  async saveCard(card: SaveCardBody) {
    return this._post<KrakenCardPublic>("/card", card);
  }

  async saveTokenizedCard(card: KrakenTokenizedCardRequestDto) {
    return this._post<KrakenCardPublic>("/card/tokenized", card);
  }

  /**
   * Updates an existing card in the Kraken API.
   * Should only be used in krakenservice.
   * @param params - The parameters containing the card token.
   * @param card - The updated card details.
   * @returns The updated card details.
   */
  async updateCard(params: UpdateCardParams, card: UpdateCardBody) {
    try {
      const response = await firstValueFrom(
        this.httpService.patch<KrakenCardPublic>(`/card/${params.token}`, card, {
          headers: {
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );

      return response.data;
    } catch (error) {
      this.krakenErrorHandler(error);
    }
  }

  /**
   * Deletes a card from the Kraken API.
   * Should only be used in krakenservice.
   * @param params - The parameters containing the card token.
   * @returns The deleted card details.
   */
  async deleteCard(params: DeleteCardParams) {
    try {
      const response = await firstValueFrom(
        this.httpService.delete<KrakenCardPublic>(`/card/${params.token}`, {
          headers: {
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );

      return response.data;
    } catch (error) {
      this.krakenErrorHandler(error);
    }
  }

  /**
   * Processes a sale transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param body - The sale transaction details.
   * @returns The transaction details.
   */
  public async sale(body: PaymentBody) {
    return this._post<KrakenTransactionPublic>("/payment/sale", body);
  }

  /**
   * Authorizes a payment through the Kraken API.
   * Should only be used in krakenservice.
   * @param body - The authorization details.
   * @returns The transaction details.
   */
  public async auth(body: PaymentBody) {
    return this._post<KrakenTransactionPublic>("/payment/auth", body);
  }

  /**
   * Captures a previously authorized transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to capture.
   * @param body - The capture details.
   * @returns The transaction details.
   */
  public async capture(transactionId: string, body: CaptureBody) {
    return this._post<KrakenTransactionPublic>(`/payment/${transactionId}/capture`, body);
  }

  /**
   * Enquires about a transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to enquire about.
   * @returns The transaction details.
   */
  public async enquire(transactionId: string) {
    return this._get<KrakenTransactionPublic>(`/payment/${transactionId}/enquire`);
  }

  /**
   * Searches for a transaction through the Kraken API.
   * @param transactionId - The ID of the transaction to search for.
   * @returns The transaction details.
   */
  public async search(transactionId: string) {
    return this._get<KrakenTransactionPublic>(`/payment/${transactionId}/search`);
  }

  /**
   * Refunds a transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to refund.
   * @param body - The refund details.
   * @returns The transaction details.
   */
  public async refund(transactionId: string, body: RefundBody) {
    return this._post<KrakenTransactionPublic>(`/payment/${transactionId}/refund`, body);
  }

  /**
   * Voids a transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to void.
   * @returns The transaction details.
   */
  public async void(transactionId: string) {
    return this._post<KrakenTransactionPublic>(`/payment/${transactionId}/void`, { reason: "voiding" });
  }

  /**
   * Sets up payer authentication.
   * Should only be used in krakenservice.
   * @param cardToken - The card token for setting up payer auth.
   * @returns The transaction details for payer auth setup.
   */
  public async payerAuthSetup(cardToken: string) {
    return this._post<KrakenTransactionPublic>(`/card/${cardToken}/payer-auth-setup`, {});
  }

  /**
   * Checks payer authentication enrolment.
   * Should only be used in krakenservice.
   * @param cardToken - The card token for checking payer auth enrolment.
   * @returns The transaction details for payer auth enrolment check.
   */
  public async checkPayerAuthEnrollment(body: PaymentConsumerAuthenticationBody) {
    return this._post<KrakenTransactionPublic>("/card/check-payer-auth-enrollment", body);
  }

  /**
   * Validates authentication results for a transaction.
   * Should only be used in krakenservice.
   * @param transactionId - The identifier of the transaction.
   * @returns The transaction details for authentication results validation.
   */
  public async validateAuthenticationResults(transactionId: string, body: ValidateAuthenticationResultsBody) {
    return this._post<KrakenTransactionPublic>(`/payment/${transactionId}/validate-authentication-results`, body);
  }
}
