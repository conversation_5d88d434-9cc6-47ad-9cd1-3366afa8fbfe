import Joi from "joi";

export interface PublishMessageForTinyUrlProcessingParams {
  timestamp: string;
  jsonPayload: {
    json_fields: {
      acceptLanguage?: string;
      ip: string;
      userAgent: string;
      originalUrl: string;
      [key: string]: any; // Allow other fields
    };
    [key: string]: any; // Allow additional fields
  };
  [key: string]: any; // Allow other fields
}

export const publishMessageForTinyUrlProcessingSchema = Joi.object({
  timestamp: Joi.string().required(),
  jsonPayload: Joi.object({
    json_fields: Joi.object({
      acceptLanguage: Joi.string().optional(),
      ip: Joi.string().required(),
      userAgent: Joi.string().required(),
      originalUrl: Joi.string().required(),
    }).unknown(true), // Allow other fields in json_fields
  }).unknown(true), // Allow other fields in jsonPayload
}).unknown(true); // Allow other fields in the outermost layer
