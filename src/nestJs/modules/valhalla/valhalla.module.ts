import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { HttpModule } from "@nestjs/axios";

import { GoogleAuthModule } from "../google-auth/google-auth.module";

import { ValhallaService } from "./valhalla.service";

@Module({
    imports: [GoogleAuthModule, ConfigModule, HttpModule],
    controllers: [],
    providers: [ValhallaService],
    exports: [ValhallaService],
})
export class ValhallaModule { }
