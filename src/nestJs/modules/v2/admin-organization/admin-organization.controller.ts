import { Body, Controller, Get, HttpStatus, Param, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { apiTags } from "@nest/modules/utils/utils/swagger.utils";
import genericSchemas from "@nest/modules/validation/dto/genericSchemas.dto";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { AdminOrganizationService } from "./admin-organization.service";
import { CreateOrganizationDto, createOrganizationSchema } from "./dto/create-organization.dto";
import { OrganizationListingQueryDto, organizationListingQuerySchema } from "./dto/organization-listing-query.dto";
import { 
  OrganizationResponseDto, 
  OrganizationDetailResponseDto, 
  OrganizationListingResponseDto 
} from "./dto/organization-response.dto";

@ApiBearerAuth()
@Controller("v2/admin/organizations")
@ApiTags(...apiTags.admin)
export class AdminOrganizationController {
  constructor(private readonly adminOrganizationService: AdminOrganizationService) {}

  @Post()
  @ApiOperation({ summary: "Create a new organization" })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: "Organization created successfully", 
    type: OrganizationResponseDto 
  })
  async createOrganization(
    @Body(new JoiValidationPipe(createOrganizationSchema)) createOrganizationDto: CreateOrganizationDto,
  ): Promise<OrganizationResponseDto> {
    const organization = await this.adminOrganizationService.createOrganization(createOrganizationDto);
    return OrganizationResponseDto.fromEntity(organization);
  }

  @Get()
  @ApiOperation({ summary: "Get all organizations with pagination and search" })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: "Organizations retrieved successfully", 
    type: OrganizationListingResponseDto 
  })
  async getOrganizations(
    @Query(new JoiValidationPipe(organizationListingQuerySchema)) query: OrganizationListingQueryDto,
  ): Promise<OrganizationListingResponseDto> {
    const result = await this.adminOrganizationService.getOrganizations(query);
    return {
      data: result.data.map(org => OrganizationResponseDto.fromEntity(org)),
      count: result.count,
    };
  }

  @Get(":organizationId")
  @ApiOperation({ summary: "Get organization by ID with all users" })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: "Organization details retrieved successfully", 
    type: OrganizationDetailResponseDto 
  })
  async getOrganizationById(
    @Param("organizationId", new JoiValidationPipe(genericSchemas.uuid.required())) organizationId: string,
  ): Promise<OrganizationDetailResponseDto> {
    const { organization, b2bUsers } = await this.adminOrganizationService.getOrganizationById(organizationId);
    return OrganizationDetailResponseDto.fromEntityWithUsers(organization, b2bUsers);
  }
}
