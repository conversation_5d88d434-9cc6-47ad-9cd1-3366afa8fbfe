import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsString, IsUUID } from "class-validator";

import { ServiceCategory } from "@nest/modules/database/entities/organizationService.entity";

export class OrganizationResponseDto {
  @ApiProperty({ description: "Organization ID", example: "123e4567-e89b-12d3-a456-************" })
  @IsUUID()
  id: string;

  @ApiProperty({ description: "Organization name", example: "Acme Hotel" })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: "Service category", 
    enum: ServiceCategory,
    example: ServiceCategory.HOTEL 
  })
  @IsEnum(ServiceCategory)
  category: ServiceCategory;

  @ApiProperty({ description: "Creation date", example: "2023-01-15T10:30:00Z" })
  createdAt: Date;

  @ApiProperty({ description: "Last update date", example: "2023-01-15T10:30:00Z" })
  updatedAt: Date;
}
