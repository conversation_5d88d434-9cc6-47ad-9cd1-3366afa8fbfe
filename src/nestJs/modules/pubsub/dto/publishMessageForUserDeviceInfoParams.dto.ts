import Joi from "joi";

export interface PublishMessageForUserDeviceInfoParams {
    userId: string;
    deviceId?: string;
    ipAddress?: string;
    time: Date;
}

export const publishMessageForUserDeviceInfoParamsSchema = Joi.object<PublishMessageForUserDeviceInfoParams>({
    userId: Joi.string().required(),
    deviceId: Joi.string().optional(),
    ipAddress: Joi.string().optional().allow(null, ""),
    time: Joi.date().required(),
}).unknown(true);