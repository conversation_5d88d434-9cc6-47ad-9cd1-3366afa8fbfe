import { Injectable, Inject } from "@nestjs/common";

import LoggerServiceAdapter from "../utils/logger/logger.service";
import NotificationTask, { NotificationUserType } from "../database/entities/notificationTask.entity";
import { NotificationSenderRequestPayloadV2 } from "../notification/notification.interface";
import { AdminDriverInboxMessageService } from "../admin/adminMerchant/modules/driver/adminDriverInboxMessage.service";
import { AdminUserInboxMessageService } from "../admin/adminUser/adminUserInboxMessage.service";
import { TemplateValidationService } from "../notification/template-validation.service";
import { UserService } from "../user/user.service";

import { DISPLAY_TYPE, MESSAGE_TYPE } from "./dto/request.dto";

@Injectable()
export class InboxCreatorService {
  constructor(
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly adminDriverInboxMessageService: AdminDriverInboxMessageService,
    private readonly adminUserInboxMessageService: AdminUserInboxMessageService,
    private readonly templateValidationService: TemplateValidationService,
    private readonly userService: UserService,
  ) { }

  async createInboxMessages(task: NotificationTask, validPayload: NotificationSenderRequestPayloadV2): Promise<void> {
    if (validPayload.recipients && validPayload.recipients.length > 0) {
      this.logger.info("🔍 [InboxCreator] Creating personalized inbox messages for V2");
      await this.createPersonalizedInboxMessages(task, validPayload);
    } else {
      this.logger.info("🔍 [InboxCreator] No recipients found, creating standard inbox messages");
      await this.createStandardInboxMessages(task, validPayload);
    }
  }

  /**
   * Create personalized inbox messages for each recipient
   */
  private async createPersonalizedInboxMessages(task: NotificationTask, validPayload: NotificationSenderRequestPayloadV2): Promise<void> {
    const { recipients, notificationRequest } = validPayload;
    const uniqueRecipients = Array.from(
      new Map((recipients ?? []).map(r => [this.templateValidationService.extractPhoneFromRecipient(r), r])).values()
    );
    for (const recipient of uniqueRecipients) {
      const recipientPhone = this.templateValidationService.extractPhoneFromRecipient(recipient);
      if (!recipientPhone) {
        this.logger.warn("[InboxCreator] No phone found for recipient:", recipient);
        continue;
      }
      const personalizedNotificationRequest = this.templateValidationService.resolveNotificationTemplates(
        notificationRequest,
        recipient
      );
      const personalizedInboxData = this.buildInboxMessageData({
        ...validPayload,
        notificationRequest: personalizedNotificationRequest
      }, task.id);
      if (task.userType === NotificationUserType.MERCHANTS) {
        await this.createMerchantInboxMessages([recipientPhone], personalizedInboxData);
      } else if (task.userType === NotificationUserType.USERS) {
        await this.createUserInboxMessages([recipientPhone], personalizedInboxData);
      }
    }
  }

  private async createStandardInboxMessages(task: NotificationTask, validPayload: NotificationSenderRequestPayloadV2): Promise<void> {
    const inboxMessageData = this.buildInboxMessageData(validPayload, task.id);
    const phoneNumbers = validPayload.recipients?.map(recipient => recipient.phone).filter(phone => phone) || [];
    if (task.userType === NotificationUserType.MERCHANTS) {
      await this.createMerchantInboxMessages(phoneNumbers, inboxMessageData);
    } else if (task.userType === NotificationUserType.USERS) {
      await this.createUserInboxMessages(phoneNumbers, inboxMessageData);
    } else {
      this.logger.warn(`Unsupported userType: ${task.userType}`);
    }
  }

  private async createMerchantInboxMessages(phoneNumbers: string[], inboxMessageData: any): Promise<void> {
    await Promise.allSettled(
      phoneNumbers.map(phoneNumber =>
        this.adminDriverInboxMessageService.createInboxMessage(phoneNumber, inboxMessageData)
          .catch(error =>
            this.logger.warn(`Failed to create inbox message for merchant ${phoneNumber}`, error)
          )
      )
    );
  }

  private async createUserInboxMessages(phoneNumbers: string[], inboxMessageData: any): Promise<void> {
    await Promise.allSettled(
      phoneNumbers.map(async phoneNumber => {
        try {
          const user = await this.userService.findByPhone(phoneNumber);
          if (user?.id) {
            await this.adminUserInboxMessageService.createInboxMessage(user.id, inboxMessageData);
          } else {
            this.logger.warn(`User not found for phone ${phoneNumber}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to create inbox message for user with phone ${phoneNumber}`, error as Error);
        }
      })
    );
  }

  private buildInboxMessageData(validPayload: NotificationSenderRequestPayloadV2, taskId?: string) {
    return {
      task_id: taskId ?? null,
      display_type: validPayload.displayType || DISPLAY_TYPE.DIALOG,
      en: {
        cta: validPayload.notificationRequest.ctaEn ?? null,
        description: validPayload.notificationRequest.bodyEn ?? null,
        title: validPayload.notificationRequest.titleEn ?? null,
        image: validPayload.notificationRequest.imageEn ?? null,
      },
      zh: {
        cta: validPayload.notificationRequest.ctaHk ?? null,
        description: validPayload.notificationRequest.bodyHk ?? null,
        title: validPayload.notificationRequest.titleHk ?? null,
        image: validPayload.notificationRequest.imageHk ?? null,
      },
      route: validPayload.route ?? null,
      message_type: validPayload.messageType || MESSAGE_TYPE.INFO,
    };
  }
}