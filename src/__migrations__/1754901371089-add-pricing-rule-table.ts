import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPricingRuleTable1754901371089 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query(
            `CREATE TABLE "pricing_rule" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "platformType" character varying NOT NULL,
                "clientType" character varying NOT NULL,
                "orderType" character varying NOT NULL,
                "jsonRule" text NOT NULL,
                "startAt" TIMESTAMP NOT NULL,
                "endAt" TIMESTAMP NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT (now() at time zone 'utc'),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT (now() at time zone 'utc'),
                CONSTRAINT "PK_pricing_rule" PRIMARY KEY ("id")
            )`
        );
        queryRunner.query(
            "CREATE INDEX \"idx_pricing_rule_types\" ON \"pricing_rule\" (\"platformType\", \"clientType\", \"orderType\")"
        );
        queryRunner.query(
            "CREATE INDEX \"idx_pricing_rule_platform_type\" ON \"pricing_rule\" (\"platformType\")"
        );
        queryRunner.query(
            "CREATE INDEX \"idx_pricing_rule_client_type\" ON \"pricing_rule\" (\"clientType\")"
        );
        queryRunner.query(
            "CREATE INDEX \"idx_pricing_rule_order_type\" ON \"pricing_rule\" (\"orderType\")"
        );
        
        // Insert default pricing rule
        queryRunner.query(
            `INSERT INTO "pricing_rule" ("platformType", "clientType", "orderType", "jsonRule", "startAt", "endAt") 
             VALUES ('DASH', 'HOTEL_SOLUTION', 'LIVE', '{"if": [{"<": [{"var": "fare"}, 70]}, 70, {"var": "fare"}]}', '2025-01-01 00:00:00', '2025-12-31 23:59:59')`
        );

        queryRunner.query(
            `INSERT INTO "pricing_rule" ("platformType", "clientType", "orderType", "jsonRule", "startAt", "endAt") 
             VALUES ('DASH', 'HOTEL_SOLUTION', 'SCHEDULED', '{"if": [{"<": [{"var": "fare"}, 70]}, 70, {"var": "fare"}]}', '2025-01-01 00:00:00', '2025-12-31 23:59:59')`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query('DELETE FROM "pricing_rule" WHERE "platformType" = \'DASH\' AND "clientType" = \'HOTEL_SOLUTION\' AND "orderType" = \'LIVE\'');
        queryRunner.query('DROP INDEX "idx_pricing_rule_types"');
        queryRunner.query('DROP INDEX "idx_pricing_rule_platform_type"');
        queryRunner.query('DROP INDEX "idx_pricing_rule_client_type"');
        queryRunner.query('DROP INDEX "idx_pricing_rule_order_type"');
        queryRunner.query('DROP TABLE "pricing_rule"');
    }

}
