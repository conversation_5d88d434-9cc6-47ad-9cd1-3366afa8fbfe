import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { TokenPayload } from "google-auth-library";
/**
 * A parameter decorator to extract the validated internal identity (token payload)
 * from the request object, which was attached by the GoogleAuthGuard.
 */
export const GetInternaGoogleAuthlIdentity = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): TokenPayload => {
        const request = ctx.switchToHttp().getRequest();
        return request.internalIdentity;
    },
);