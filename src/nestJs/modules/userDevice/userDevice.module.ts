import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { HttpModule } from "@nestjs/axios";

import { UserDeviceRepository } from "../database/repositories/userDevice.repository";

import { UserDeviceService } from "./userDevice.service";

@Module({
  imports: [HttpModule, ConfigModule],
  providers: [UserDeviceRepository, UserDeviceService],
  exports: [UserDeviceService],
})
export class UserDeviceModule { }