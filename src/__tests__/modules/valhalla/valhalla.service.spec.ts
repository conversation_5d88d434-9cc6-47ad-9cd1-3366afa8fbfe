import { Test, TestingModule } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";

import { ValhallaService } from "../../../nestJs/modules/valhalla/valhalla.service";
import { GoogleAuthService } from "../../../nestJs/modules/google-auth/google-auth.service";
import { ValhallaProxyRouteRequestBody } from "../../../nestJs/modules/valhalla/valhalla.dto";
import { HttpService } from "@nestjs/axios";
import { plainToInstance } from "class-transformer";

jest.mock("@nestjs/axios");
jest.mock("../../../nestJs/modules/google-auth/google-auth.service")
jest.mock("@nestjs/config");
jest.mock("@nestjs/cache-manager");
jest.mock("rxjs")

describe("ValhallaService", () => {
    let service: ValhallaService;
    let configService: ConfigService;
    let googleAuthService: GoogleAuthService;
    let cacheManager: Cache;
    let httpService: HttpService;

    beforeEach(async () => {
        const cacheManagerMock = {
            wrap: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ValhallaService,
                GoogleAuthService,
                ConfigService,
                { provide: CACHE_MANAGER, useValue: cacheManagerMock },
                HttpService
            ],
        }).compile();

        service = module.get(ValhallaService);
        googleAuthService = module.get(GoogleAuthService);
        configService = module.get(ConfigService);
        cacheManager = module.get(CACHE_MANAGER);
        httpService = module.get(HttpService);
    });

    it("should be defined", () => {
        expect(service).toBeDefined();
        expect(googleAuthService).toBeDefined();
        expect(configService).toBeDefined();
        expect(cacheManager).toBeDefined();
        expect(httpService).toBeDefined();
    });

    describe("getRoute", () => {
        it("should get route and cache the response", async () => {
            const mockUrl = "https://valhalla.example.com/route";
            const mockToken = "mocked-id-token";
            const mockRequestBody: ValhallaProxyRouteRequestBody = plainToInstance(ValhallaProxyRouteRequestBody, {
                locations: [
                    { lat: 22.3964, lon: 114.1095 },
                    { lat: 22.2783, lon: 114.1747 },
                ],
                costing: "auto",
                directions_options: { units: "kilometers" },
            });


            const googleAuthSpy = jest.spyOn(googleAuthService, "getIdToken").mockResolvedValue(mockToken);
            const getValhallaRouteSpy = jest.spyOn(service as any, "getValhallaRouteUrl").mockReturnValue(mockUrl);

            const httpServiceSpy = jest.spyOn(httpService, "post")

            await service.getRoute(mockRequestBody);

            expect(googleAuthSpy).toHaveBeenCalledWith(mockUrl);
            expect(httpServiceSpy).toHaveBeenCalledWith(
                mockUrl,
                mockRequestBody,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${mockToken}`,
                    },
                }
            );
            expect(getValhallaRouteSpy).toHaveBeenCalled()

        });
    });

    describe("getValhallaRouteUrl", () => {
        it("should return the correct Valhalla route URL", () => {
            const mockUrl = "https://valhalla.example.com";
            const configServiceSpy = jest.spyOn(configService, "getOrThrow").mockReturnValue(mockUrl);

            const result = (service as any)["getValhallaRouteUrl"]();

            expect(result).toBe(`${mockUrl}/route`);
            expect(configServiceSpy).toHaveBeenCalledWith("VALHALLA_URL");
        });
    })
});