import { Repository } from "typeorm";
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import UserDeviceEntity from "../entities/userDevice.entity";

@Injectable()
export class UserDeviceRepository extends Repository<UserDeviceEntity> {
  constructor(
    @InjectRepository(UserDeviceEntity) private readonly userDeviceRepository: Repository<UserDeviceEntity>,
  ) {
    super(userDeviceRepository.target, userDeviceRepository.manager, userDeviceRepository.queryRunner);
  }

  async upsertUserDevice(userDeviceEntity: UserDeviceEntity): Promise<void> {
    await this.userDeviceRepository.createQueryBuilder()
      .insert()
      .into(UserDeviceEntity)
      .values(userDeviceEntity)
      .orUpdate(["updatedAt"], ["userId", "deviceId", "ipAddress"])
      .execute();
  }
}