import { CollectionGroup } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { HeartbeatDocument } from "../documents/heartbeat.document";

/**
 * Repository for heartbeat
 */
class HeartbeatRepository {
  heartbeatCollection: CollectionGroup<HeartbeatDocument>;

  constructor(heartbeatCollection: CollectionGroup<HeartbeatDocument>, logger: LoggerServiceAdapter) {
    this.heartbeatCollection = heartbeatCollection;
  }

  async batchGet(query: FirebaseFirestore.Query<HeartbeatDocument>): Promise<HeartbeatDocument[]> {
    const snapshot = await query.get();
    return snapshot.docs.map((doc) => {
      const data = doc.data() as HeartbeatDocument;
      const segments = doc.ref.path.split("/");
      const meterId = segments[1];
      return { ...data, meter_id: meterId };
    });
  }
}

export default HeartbeatRepository;
