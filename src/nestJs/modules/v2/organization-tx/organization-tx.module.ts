import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";

import { OrganizationTxController } from "./organization-tx.controller";
import { OrganizationTxService } from "./organization-tx.service";

@Module({
  controllers: [OrganizationTxController],
  providers: [OrganizationTxService, TxRepository, PaymentTxRepository],
  exports: [OrganizationTxService],
})
export class OrganizationTxModule {}