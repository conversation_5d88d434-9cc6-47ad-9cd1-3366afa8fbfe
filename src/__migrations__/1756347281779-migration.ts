import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1756347281779 implements MigrationInterface {
  name = "Migration1756347281779";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_instrument" ALTER COLUMN "cardType" TYPE varchar');
    await queryRunner.query('DROP TYPE "public"."payment_instrument_cardtype_enum"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_instrument_cardtype_enum\" AS ENUM('VISA', 'MASTERCARD')",
    );
    await queryRunner.query(
      'ALTER TABLE "payment_instrument" ALTER COLUMN "cardType" SET DATA TYPE "public"."payment_instrument_cardtype_enum" USING "cardType"::"public"."payment_instrument_cardtype_enum"',
    );
  }
}
