import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateUserDeviceTable1692547200000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "user_device" (
        "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID NOT NULL REFERENCES "user"("id") ON DELETE NO ACTION,
        "deviceId" VARCHAR,
        "ipAddress" VARCHAR,
        "createdAt" TIMESTAMP DEFAULT NOW(),
        "updatedAt" TIMESTAMP DEFAULT NOW()
      );
    `);
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`
      DROP TABLE "user_device";
    `);
  }
}