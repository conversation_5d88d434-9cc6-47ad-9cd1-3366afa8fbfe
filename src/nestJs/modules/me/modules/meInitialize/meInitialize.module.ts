import { Modu<PERSON> } from "@nestjs/common";

import { UserDeviceRepository } from "@nest/modules/database/repositories/userDevice.repository";

import { UserRepository } from "../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";
import { UserModule } from "../../../user/user.module";
import { MeNotificationTokenModule } from "../meNotificationToken/meNotificationToken.module";
import { PubSubModule } from "../../../pubsub/pubsub.module";

import { MeInitializeService } from "./meInitialize.service";
import { MeInitializeController } from "./meInitialize.controller";

@Module({
  providers: [
    MeInitializeController,
    MeInitializeService,
    UserRepository,
    UserNotificationTokenRepository,
    UserDeviceRepository,
  ],
  imports: [MeNotificationTokenModule, UserModule, PubSubModule],
  controllers: [MeInitializeController],
  exports: [MeInitializeController, MeInitializeService],
})
export class MeInitializeModule {}
