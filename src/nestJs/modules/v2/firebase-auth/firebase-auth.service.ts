import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { firstValueFrom } from "rxjs";
import { getAuth } from "firebase-admin/auth";
import { AuthUserRecord } from "firebase-functions/v2/identity";

import { FirebaseAuthResultsDto } from "@nest/modules/auth/dto/firebase-auth-results.dto";
import { LogAll } from "@nest/decorators/log-all.decorator";


@LogAll()
@Injectable()
export class FirebaseAuthService {
  private readonly apiKey: string;

  constructor(private readonly httpService: HttpService, private readonly configService: ConfigService) {
    this.apiKey = this.configService.getOrThrow<string>("WEB_API_KEY");
  }
  async signupWithEmailAndPhoneNumberAndPassword(email: string, password: string): Promise<FirebaseAuthResultsDto> {
    const response = await firstValueFrom(
      this.httpService.post<FirebaseAuthResultsDto>(`/accounts:signUp?key=${this.apiKey}`, {
        email,
        password,
        returnSecureToken: true,
      }),
    );

    return response.data;
  }

  async signInWithEmailAndPassword(email: string, password: string): Promise<FirebaseAuthResultsDto> {
    const response = await firstValueFrom(
      this.httpService.post<FirebaseAuthResultsDto>(`/accounts:signInWithPassword?key=${this.apiKey}`, {
        email,
        password,
        returnSecureToken: true,
      }),
    );

    return response.data;
  }

  async updateUserPhoneNumber(uid: string, phoneNumber: string): Promise<void> {
    const firebaseAuth = getAuth();

    await firebaseAuth.updateUser(uid, { phoneNumber });
  }

  async getUserByEmail(email: string): Promise<AuthUserRecord> {
    const firebaseAuth = getAuth();

    const user = await firebaseAuth.getUserByEmail(email);

    return user;
  }

  async getCustomToken(uid: string, data?: Record<string, unknown>): Promise<string> {
    const firebaseAuth = getAuth();

    const customToken = await firebaseAuth.createCustomToken(uid, data);

    return customToken;
  }
}
