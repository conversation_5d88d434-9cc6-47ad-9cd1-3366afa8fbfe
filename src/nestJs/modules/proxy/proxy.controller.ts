import { Body, Controller, Inject, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiTags, getSchemaPath } from "@nestjs/swagger";

import { AuthGuard } from "@nest/infrastructure/guards/auth.guard";
import { CurrentUser, FirebaseUser } from "@nest/decorators/current-user.decorator";

import { apiTags } from "../utils/utils/swagger.utils";
import { ValhallaProxyRouteRequestBody } from "../valhalla/valhalla.dto";
import { ValhallaService } from "../valhalla/valhalla.service";
import LoggerServiceAdapter from "../utils/logger/logger.service";

/**
 * Proxy controller
 */
@Controller("proxy")
@ApiBearerAuth()
@ApiTags(...apiTags.proxy)
export class ProxyController {
    constructor(private readonly valhallaService: ValhallaService, @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,) { }

    @Post("/valhalla/route")
    @UseGuards(AuthGuard)
    @ApiBody({
        description: "Valhalla route request body",
        type: ValhallaProxyRouteRequestBody,
        schema: {
            $ref: getSchemaPath(ValhallaProxyRouteRequestBody),
        },
        examples: {
            example1: {
                summary: "Sample request",
                value: {
                    locations: [
                        { lat: 22.3964, lon: 114.1095 },
                        { lat: 22.2783, lon: 114.1747 }
                    ],
                    costing: "auto",
                    directions_options: { units: "kilometers" }
                }
            }
        }
    })
    async getValhallaRoute(@CurrentUser() user: FirebaseUser, @Body() valhallaProxyRouteRequestBody: ValhallaProxyRouteRequestBody) {
        const startRequestTime = performance.now()
        const { requestId, user_id } = user
        this.logger.info("Processing Valhalla route request:", { valhallaProxyRouteRequestBody, requestId, user_id });

        const response = await this.valhallaService.getRoute(valhallaProxyRouteRequestBody);
        const { status, data, statusText } = response;
        const endRequestTime = performance.now()
        this.logger.info("Valhalla route response:", {
            requestId,
            user_id,
            status,
            data,
            statusText,
            processingTime: `${endRequestTime - startRequestTime}ms`,
        });

        return { status, data, statusText }
    }
}
