import { Body, Controller, Param, Post, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { B2bUsersService } from "./services/b2b-users.service";
import { CreateB2bUserDto, LoginB2bUserDto, LoginB2bUserResponseDto, UpdateB2bUserDto } from "./b2b-user.dto";

@ApiTags("B2B Users")
@Controller("v2/b2b-users")
export class B2bUserController {
  constructor(private readonly b2bUsersService: B2bUsersService) {}

  @Post("/")
  @ApiOperation({ summary: "Create a new B2B user" })
  @ApiResponse({ status: 201, description: "User created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 409, description: "User already exists" })
  async createB2bUser(@Body() body: CreateB2bUserDto) {
    return this.b2bUsersService.createB2bUser(body);
  }

  @Post("login")
  @ApiOperation({ summary: "Login B2B user with email and password" })
  @ApiResponse({ 
    status: 200, 
    description: "Login successful", 
    type: LoginB2bUserResponseDto 
  })
  @ApiResponse({ status: 401, description: "Invalid credentials" })
  @ApiResponse({ status: 400, description: "Bad request" })
  async login(@Body() body: LoginB2bUserDto): Promise<LoginB2bUserResponseDto> {
    return this.b2bUsersService.loginWithEmailAndPassword(body);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update B2B user" })
  @ApiResponse({ status: 200, description: "User updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "User not found" })
  async updateB2bUser(@Body() body: UpdateB2bUserDto, @Param("id") id: string) {
    return this.b2bUsersService.updateB2bUser(id, body);
  }
}
