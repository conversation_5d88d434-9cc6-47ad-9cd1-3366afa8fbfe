import { Injectable, Inject } from "@nestjs/common";

import LoggerServiceAdapter from "../utils/logger/logger.service";

import { TemplateValidationService } from "./template-validation.service";

@Injectable()
export class RecipientsProcessorService {
  constructor(
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly templateValidationService: TemplateValidationService,
  ) { }

  processRecipientsData(payload: any, recipients: Array<Record<string, any>>) {
    if (payload.notificationRequest) {
      this.validateTemplatesWithRecipients(payload.notificationRequest, recipients);
    }
    const phoneNumbers = this.templateValidationService.extractPhoneNumbers(recipients);

    if (phoneNumbers.length === 0) {
      throw new Error("No valid phone numbers found in recipients. Make sure your CSV has a 'phone' column.");
    }

    this.logger.info(`[RecipientsProcessorService] Processed ${recipients.length} recipients, extracted ${phoneNumbers.length} phone numbers`);

    return {
      notificationRequest: payload.notificationRequest,
      recipients: recipients,
    };
  }

  private validateTemplatesWithRecipients(notificationRequest: any, recipients: Array<Record<string, any>>): void {
    const templates = {
      titleEn: notificationRequest?.titleEn,
      bodyEn: notificationRequest?.bodyEn,
      titleHk: notificationRequest?.titleHk,
      bodyHk: notificationRequest?.bodyHk,
    };

    // Remove undefined/null templates
    const validTemplates = Object.fromEntries(
      Object.entries(templates).filter(([_, value]) => value)
    );

    if (Object.keys(validTemplates).length === 0) {
      return;
    }

    try {
      const validation = this.templateValidationService.validateTemplatesWithRecipients(validTemplates, recipients);

      if (!validation.isValid) {
        const errorMessage = "Template validation failed!\n" +
          `Missing CSV columns: [${validation.missingVariables.join(", ")}]\n` +
          `Available CSV columns: [${validation.availableColumns.join(", ")}]\n` +
          `Variables found in templates: [${validation.foundVariables.join(", ")}]`;

        throw new Error(errorMessage);
      }

      this.logger.info("[RecipientsProcessorService] Template validation passed", {
        foundVariables: validation.foundVariables,
        recipientColumns: validation.availableColumns,
      });
    } catch (error) {
      this.logger.error("[RecipientsProcessorService] Template validation failed", {}, error as Error);
      throw error;
    }
  }
}