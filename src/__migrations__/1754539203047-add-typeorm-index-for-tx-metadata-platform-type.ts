import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTypeormIndexForTxMetadataPlatformType1754539203047 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('CREATE INDEX CONCURRENTLY "idx_tx_metadata_platformType" ON "tx" ((metadata->>\'platformType\'))');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX "idx_tx_metadata_platformType"');
    }

    public get transaction(): boolean {
        return false;
    }
}
