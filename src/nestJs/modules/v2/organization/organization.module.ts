import { Modu<PERSON> } from "@nestjs/common";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { OrganizationRepository } from "@nest/modules/database/repositories/organization.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";

import { OrganizationController } from "./organization.controller";
import { OrganizationService } from "./services/organization.service";

@Module({
  imports: [AppDatabaseModule],
  providers: [OrganizationService, OrganizationRepository, UserRepository],
  controllers: [OrganizationController],
  exports: [OrganizationService],
})
export class OrganizationModule {}
