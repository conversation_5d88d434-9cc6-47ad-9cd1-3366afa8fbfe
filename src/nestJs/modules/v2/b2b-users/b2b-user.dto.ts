import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsString, IsUUID } from "class-validator";

import { AppUser } from "@nest/modules/user/dto/user.dto";

export class CreateB2bUserDto {
  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  password: string;

  @ApiProperty()
  @IsUUID()
  organizationId: string;
}

export class UpdateB2bUserDto {
  @ApiProperty()
  @IsString()
  address: string;
}

export class LoginB2bUserDto {
  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  password: string;
}

export class LoginB2bUserResponseDto {
  @ApiProperty({
    description: "Firebase custom token for authentication",
    example: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  })
  customToken: string;

  @ApiProperty({
    description: "Firebase ID token",
    example: "eyJhbGciOiJSUzI1NiIsImtpZCI6IjFlOWdkazcifQ...",
    required: false
  })
  idToken?: string;

  @ApiProperty({
    description: "Firebase refresh token",
    example: "AEu4IL2K6PXMgZmGvq7Q8Z...",
    required: false
  })
  refreshToken?: string;

  @ApiProperty({
    description: "Token expiration time",
    example: "3600",
    required: false
  })
  expiresIn?: string;

  @ApiProperty({
    description: "User local ID",
    example: "abc123def456"
  })
  localId: string;

  @ApiProperty({
    description: "User email address",
    example: "<EMAIL>"
  })
  email: string;

  @ApiProperty({
    description: "User phone number",
    example: "+1234567890"
  })
  phoneNumber: string;

  @ApiProperty({
    description: "User object with additional details",
    type: "object"
  })
  user: AppUser;
}