import { PaymentGatewayTypes } from "@nest/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "@nest/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "@nest/modules/payment/dto/paymentInformationType.dto";
import { randomUUID } from "crypto";

export const mockPaymentTxRepositoryFactory = {
  mockPaymentTxFindOne: (paymentTxRepository: any, paymentTx: Partial<any> = {}, isReturnNull = false) => {
    jest.spyOn(paymentTxRepository, "findOne").mockImplementation(async () => {
      if (isReturnNull) {
        return null;
      }
      return {
        ...mockPaymentTxRepositoryFactory.createFakePaymentTx(),
        ...paymentTx,
      };
    });
  },
  mockPaymentTxSave: (paymentTxRepository: any, paymentTx: Partial<any> = {}, isReturnNull = false) => {
    jest.spyOn(paymentTxRepository, "save").mockImplementation(async () => {
      if (isReturnNull) {
        return null;
      }

      return {
        ...mockPaymentTxRepositoryFactory.createFakePaymentTx(),
        ...paymentTx,
      };
    });
  },
  createFakePaymentTx: (overrides: Partial<any> = {}) => {
    const baseDate = new Date();
    return {
      id: randomUUID(),
      amount: 120.5,
      gatewayTransactionId: "gw_tx_" + randomUUID(),
      cardNumber: "****1234",
      paymentMethod: "VISA",
      gateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
      gatewayResponse: {
        status: "SUCCESS",
        transactionId: "gw_tx_" + randomUUID(),
        authCode: "AUTH123",
      },
      status: PaymentInformationStatus.SUCCESS,
      type: PaymentInformationType.SALE,
      createdAt: baseDate,
      updatedAt: baseDate,
      ...overrides,
    };
  },
};
