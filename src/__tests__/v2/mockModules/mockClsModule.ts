import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";

export const mockClsContextStorageService = {
  get: jest.fn(),
  setContextId: jest.fn(),
  setAppUserId: jest.fn(),
  getAppUserId: jest.fn(),
  setCreateHailingRequestTxId: jest.fn(),
  setToken: jest.fn(),
  getToken: jest.fn(),
  getContextId: jest.fn(),
  getContext: jest.fn(),
  getCreateHailingRequestTxId: jest.fn(),
  set: jest.fn(),
};

export const MockClsContextStorageServiceProvider = {
  provide: ClsContextStorageService,
  useValue: mockClsContextStorageService,
};
