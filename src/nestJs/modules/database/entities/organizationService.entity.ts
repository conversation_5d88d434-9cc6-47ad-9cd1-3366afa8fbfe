import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import OrganizationEntity from "./organization.entity";
import Merchant from "./merchant.entity";

export enum ServiceCategory {
  HOTEL = "HOTEL",
  FLEET = "FLEET",
}

export enum OrganizationPaymentMethod {
  ON_DEMAND_PAYMENT = "ON_DEMAND_PAYMENT",
  RECURRING = "RECURRING",
}

export enum OrganizationRecurringPeriod {
  DAILY = "DAILY",
  WEEKLY = "WEEKLY",
  MONTHLY = "MONTHLY",
}

@Entity({ name: "organization_service" })
export default class OrganizationServiceEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;
  
  @Column()
  category: ServiceCategory;

  @Column({ default: OrganizationPaymentMethod.ON_DEMAND_PAYMENT })
  billPaymentMethod: OrganizationPaymentMethod;

  @Column({ nullable: true, default: OrganizationRecurringPeriod.MONTHLY })
  billRecurringPeriod?: OrganizationRecurringPeriod;

  @Column({ nullable: true })
  billPaymentStartDate?: Date;

  @JoinColumn({ name: "billMerchantId" })
  @Column({ nullable: true })
  billMerchantId?: string;

  @ManyToOne(() => Merchant, { nullable: true })
  billMerchant?: Merchant;
  
  @JoinColumn({ name: "payoutMerchantId" })
  @Column({ nullable: true })
  payoutMerchantId?: string;

  @ManyToOne(() => Merchant, { nullable: true })
  payoutMerchant?: Merchant;

  @Column({ nullable: true, default: OrganizationPaymentMethod.ON_DEMAND_PAYMENT })
  payoutPaymentMethod?: OrganizationPaymentMethod;

  @Column({ nullable: true, default: OrganizationRecurringPeriod.MONTHLY })
  payoutRecurringPeriod?: OrganizationRecurringPeriod;

  @Column({ nullable: true })
  payoutPaymentStartDate?: string;

  @JoinColumn({ name: "organizationId" })
  organizationId: string;

  @ManyToOne(() => OrganizationEntity, (organization) => organization.id)
  organization: OrganizationEntity;
}
