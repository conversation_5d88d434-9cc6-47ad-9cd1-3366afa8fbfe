import { ApiProperty } from "@nestjs/swagger";

import OrganizationEntity from "@nest/modules/database/entities/organization.entity";
import User from "@nest/modules/database/entities/user.entity";
import { ListingResponseDto } from "@nest/modules/validation/dto/listingResponseSchema.dto";
import { B2bUserDocument } from "@nest/modules/appDatabase/documents/b2bUser.document";
import OrganizationServiceEntity from "@nest/modules/database/entities/organizationService.entity";

export class OrganizationResponseDto {
  @ApiProperty({ description: "Organization ID" })
  id: string;

  @ApiProperty({ description: "Organization name" })
  name: string;

  @ApiProperty({ description: "Organization category" })
  category: string;

  @ApiProperty({ description: "Organization services" })
  services: OrganizationServiceEntity[];

  @ApiProperty({ description: "Created at" })
  createdAt: Date;

  @ApiProperty({ description: "Updated at" })
  updatedAt: Date;

  static fromEntity(organization: OrganizationEntity): OrganizationResponseDto {
    return {
      id: organization.id,
      name: organization.name,
      category: organization.category,
      services: organization.services,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,
    };
  }
}

export class OrganizationDetailResponseDto extends OrganizationResponseDto {
  @ApiProperty({ description: "Users in organization", type: [Object] })
  users: User[];

  @ApiProperty({ description: "Firebase users in organization", type: [Object] })
  b2bUsers: B2bUserDocument[];

  static fromEntityWithUsers(
    organization: OrganizationEntity,
    b2bUsers: B2bUserDocument[],
  ): OrganizationDetailResponseDto {
    const base = OrganizationResponseDto.fromEntity(organization);
    return {
      ...base,
      users: organization.users || [],
      b2bUsers: b2bUsers || [],
    };
  }
}

export class OrganizationListingResponseDto extends ListingResponseDto<OrganizationResponseDto> {
  @ApiProperty({ type: [OrganizationResponseDto] })
  data: OrganizationResponseDto[];
}
