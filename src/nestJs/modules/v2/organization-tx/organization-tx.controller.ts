import { Controller, Get, Param, Query } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiBadRequestResponse, ApiBearerAuth, ApiTags, ApiParam } from "@nestjs/swagger";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "@nest/modules/utils/utils/swagger.utils";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { 
  OrganizationTxQueryDto, 
  OrganizationTxResponseDto, 
  organizationTxQuerySchema 
} from "./dto/organization-tx.dto";
import { OrganizationTxService } from "./organization-tx.service";

@Controller("v2/organizations/:organizationId/transactions")
@ApiBearerAuth()
@ApiTags(...apiTags.admin)
export class OrganizationTxController {
  constructor(private readonly organizationTxService: OrganizationTxService) {}

  @Get("/")
  @ApiOperation({
    summary: "Get organization transactions",
    description: "List all transactions for an organization filtered by type (TRIP or HAILING_REQUEST) with pagination",
  })
  @ApiParam({
    name: "organizationId",
    description: "Organization ID",
    type: "string",
  })
  @ApiResponse({ 
    status: 200, 
    description: "List of organization transactions",
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.organization.notFound("[organizationId]"),
    ]),
  })
  async getOrganizationTransactions(
    @Param("organizationId") organizationId: string,
    @Query(new JoiValidationPipe(organizationTxQuerySchema)) query: OrganizationTxQueryDto,
  ): Promise<OrganizationTxResponseDto> {
    return this.organizationTxService.getOrganizationTransactions(organizationId, query);
  }
}
