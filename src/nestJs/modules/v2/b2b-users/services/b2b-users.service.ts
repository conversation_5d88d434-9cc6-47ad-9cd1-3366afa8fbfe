import { Injectable } from "@nestjs/common";

import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { OrganizationRepository } from "@nest/modules/database/repositories/organization.repository";
import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";

import { FirebaseAuthService } from "../../firebase-auth/firebase-auth.service";
import { CreateB2bUserDto, LoginB2bUserDto, LoginB2bUserResponseDto, UpdateB2bUserDto } from "../b2b-user.dto";

@Injectable()
export class B2bUsersService {
  constructor(
    private readonly firebaseAuthService: FirebaseAuthService,
    private readonly userRepository: UserRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  async createB2bUser(body: CreateB2bUserDto) {
    const user = await this.userRepository.findUserByPhoneNumberAndEmail(body.phoneNumber, body.email);

    if (user) {
      throw errorBuilder.user.existingUserFound(body.email, body.phoneNumber);
    }

    const newAuthUser = await this.firebaseAuthService.signupWithEmailAndPhoneNumberAndPassword(
      body.email,
      body.password,
    );

    const organization = await this.organizationRepository.findOne({
      where: { id: body.organizationId },
    });

    if (!organization) {
      throw errorBuilder.user.organizationNotFound(body.organizationId);
    }

    await this.appDatabaseService.b2bUserRepository().create({
      id: newAuthUser.localId,
      localId: newAuthUser.localId,
      email: body.email,
      phoneNumber: body.phoneNumber,
      organizationId: organization.id,
    });

    const newUser = await this.userRepository.createUser({
      appDatabaseId: newAuthUser.localId,
      phoneNumber: body.phoneNumber,
      email: body.email,
      organizationId: organization.id,
    });

    if (newUser.appDatabaseId) {
      await this.appDatabaseService.b2bUserRepository().updateDocumentAndGetResult(newUser.appDatabaseId, {
        databaseId: newUser.id,
      });
    }

    return newUser;
  }

  async updateB2bUser(id: string, body: UpdateB2bUserDto) {
    const user = await this.userRepository.findOne({ where: { id } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(id);
    }

    if (user.appDatabaseId) {
      await this.appDatabaseService.b2bUserRepository().updateDocumentAndGetResult(user.appDatabaseId, {
        address: body.address,
        databaseId: user.id,
      });
    }

    return user;
  }

  async loginWithEmailAndPassword(body: LoginB2bUserDto): Promise<LoginB2bUserResponseDto> {
    const authUser = await this.firebaseAuthService.signInWithEmailAndPassword(body.email, body.password);
    if (!authUser) {
      throw errorBuilder.user.invalidCredentials();
    }

    const user = await this.userRepository.findAppUserById(authUser.localId);

    const authUserRecord = await this.firebaseAuthService.getUserByEmail(body.email);

    await this.firebaseAuthService.updateUserPhoneNumber(authUserRecord.uid, user.phoneNumber);

    const customToken = await this.firebaseAuthService.getCustomToken(authUserRecord.uid);

    return {
      customToken,
      idToken: authUser.idToken,
      refreshToken: authUser.refreshToken,
      expiresIn: authUser.expiresIn,
      localId: authUser.localId,
      email: authUser.email,
      phoneNumber: user.phoneNumber,
      user,
    };
  }
}
