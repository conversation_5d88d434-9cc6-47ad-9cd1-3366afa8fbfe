import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import OrganizationServiceEntity from "../entities/organizationService.entity";

@Injectable()
export class OrganizationServiceRepository extends Repository<OrganizationServiceEntity> {
  constructor(@InjectRepository(OrganizationServiceEntity) repository: Repository<OrganizationServiceEntity>) {
    super(repository.target, repository.manager, repository.queryRunner);
  }
}
