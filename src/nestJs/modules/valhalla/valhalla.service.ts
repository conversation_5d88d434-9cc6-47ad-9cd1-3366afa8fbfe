import { Injectable } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { lastValueFrom } from "rxjs";

import { GoogleAuthService } from "../google-auth/google-auth.service";

import { ValhallaProxyRouteRequestBody } from "./valhalla.dto";

@Injectable()
export class ValhallaService {

    constructor(
        private readonly googleAuthService: GoogleAuthService,
        private readonly configService: ConfigService,
        private readonly httpService: HttpService
    ) { }

    async getRoute(valhallaProxyRouteRequestBody: ValhallaProxyRouteRequestBody) {
        const url = this.getValhallaRouteUrl();
        const token = await this.googleAuthService.getIdToken(url);
        return lastValueFrom(this.httpService.post(url, valhallaProxyRouteRequestBody, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            }
        }));
    }

    private getValhallaRouteUrl() {
        const baseUrl = this.configService.getOrThrow("VALHALLA_URL");
        return `${baseUrl}/route`;
    }
}