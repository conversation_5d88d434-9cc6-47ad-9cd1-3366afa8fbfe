import { CampaignService } from "@nest/modules/campaign/campaign.service";

export const mockCampaignService = {
  createCampaign: jest.fn(),
  updateCampaign: jest.fn(),
  queryCampaigns: jest.fn(),
  getApplicableCampaign: jest.fn(),
  getCampaignsListWithAggregatedValues: jest.fn(),
  getApplicableCampaigns: jest.fn().mockImplementation(() => [undefined, undefined]),
};

export const MockCampaignServiceProvider = {
  provide: CampaignService,
  useValue: mockCampaignService,
};
