import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTypeormIndexForTxMetadataGinIndex1754539402412 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('CREATE INDEX CONCURRENTLY "idx_tx_metadata_gin_index" ON "tx" USING GIN (metadata jsonb_path_ops)');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX "idx_tx_metadata_gin_index"');
    }

    public get transaction(): boolean {
        return false;
    }
}
