# Firebase Utility Function Updates

## serializeFirestoreData Function

The `serializeFirestoreData` function has been updated to properly handle Firestore event data and resolve the `toJSON` issue.

### Key Changes

1. **Added proper TypeScript typing** - Replaced `any` types with proper type definitions
2. **Added toJSON method handling** - The function now properly handles objects with `toJSON()` methods (like Firestore document snapshots)
3. **Updated imports** - Using individual imports from `firebase-admin/firestore` instead of the deprecated pattern
4. **Improved error safety** - Added proper null/undefined checks and object property validation

### Usage with Firestore Events

```typescript
import { FirestoreEvent, Change, DocumentSnapshot } from "firebase-functions/v2/firestore";
import { serializeFirestoreData } from "./utils/firebase";

export const handleDocumentUpdate = (event: FirestoreEvent<Change<DocumentSnapshot> | undefined>) => {
  // Get raw Firestore data
  const rawData = event.data?.after.data();
  
  // Serialize it to handle toJSON and convert Firestore types
  const serializedData = serializeFirestoreData(rawData);
  
  // Now you can safely work with the data
  console.log("Serialized data:", serializedData);
};
```

### What the Function Handles

- **Null/undefined values** - Returns `null` for both
- **Primitive types** - Strings, numbers, booleans pass through unchanged
- **Firestore Timestamps** - Converted to ISO string format
- **GeoPoints** - Converted to `{ latitude, longitude }` objects
- **DocumentReferences** - Converted to path strings
- **Objects with toJSON()** - Calls `toJSON()` and recursively processes the result
- **Arrays** - Recursively processes each element
- **Plain objects** - Recursively processes each property

### Before and After

**Before (problematic):**
```typescript
// This would cause toJSON issues
const data = event.data?.after.data();
// Data might contain Firestore-specific types that cause serialization issues
```

**After (fixed):**
```typescript
// This properly handles all Firestore types and toJSON methods
const rawData = event.data?.after.data();
const serializedData = serializeFirestoreData(rawData);
// serializedData is now safe to use, log, or transmit
```

### Benefits

1. **Resolves toJSON issues** - Properly handles Firestore document snapshots
2. **Type safety** - Proper TypeScript types prevent runtime errors
3. **Consistent serialization** - All Firestore types are converted to JSON-safe formats
4. **Recursive processing** - Handles deeply nested objects and arrays
5. **Performance** - Efficient processing with proper null checks and early returns
