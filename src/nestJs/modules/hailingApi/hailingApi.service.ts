import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { firstValueFrom } from "rxjs";

import { LogAll } from "@nest/decorators/log-all.decorator";

import { MeterDocument } from "../appDatabase/documents/meter.document";
import { HailUpdateResponse, IUpdateFleetOrderResponse } from "../cloudTaskFleetOrder/interface";
import FleetOrderEntity from "../database/entities/fleetOrder.entity";
import { HailStatus } from "../hailing/dto/updateHail.dto";
import { Heartbeat } from "../transaction/dto/txEventType.dto";
import {
  HailingApiCreateOrderResponse,
  HailingApiGetPriceEstimationResponse,
  HailingApiGetPriceEstimationResponseV2,
} from "../hailing/dto/hailing.api.dto";
import { HailingItineraryStepResponse } from "../hailing/dto/hailing.dto";
import { HailingCreateOrderBody } from "../me/modules/meHailing/dto/meHailing.dto";
import { TxHailingRequest } from "../transaction/dto/tx.dto";
import PaymentTx from "../database/entities/paymentTx.entity";
import { errorBuilder } from "../utils/utils/error.utils";
import { roundUpOneDecimal } from "../utils/utils/number.utils";
import { FleetVehicleType } from "../appDatabase/documents/fleetVehicleType.document";
import ClsContextStorageService from "../utils/context/clsContextStorage.service";
import Tx from "../database/entities/tx.entity";
import { TripEstimation } from "../location/dto/location.dto";
import {
  CreateHailQuoteResponse,
  HailingCreateOrderBodyV3,
  HailingGetQuoteBodyV3,
} from "../v2/hailing/dto/create-hail.dto";
import { HailingDiscount } from "../v2/hailing/dto/discount.dto";
import dayjs from "../utils/dayjs";
import PricingRuleEntity from "../database/entities/pricingRule.entity";
import { HailingCancellationFeeBreakdown } from "../transaction/dto/txHailingRequest.dto";

import { HailingApiPaymentDetailsDto } from "./dto.hailingApi.dto";

@LogAll()
@Injectable()
export class HailingApiService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private clsService: ClsContextStorageService,
  ) {}

  async updateDriverHeartBeat(phoneNumber: string, hailId: string, heartBeat: Heartbeat): Promise<void> {
    const response = await firstValueFrom(
      this.httpService.patch<void>(
        "v1/drivers/update_heart_beat",
        {
          hailId,
          heartBeat,
          phoneNumber,
        },
        {
          headers: {
            "X-API-KEY": this.configService.getOrThrow("CLOUD_TASKS_API_KEY"),
            "x-correlation-id": this.clsService.getContextId(),
          },
        },
      ),
    );
    return response.data;
  }

  async updateFleetHailingRequest(
    fleetOrder: FleetOrderEntity,
    result: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
    hailStatus: HailStatus,
  ): Promise<HailUpdateResponse> {
    const response = await firstValueFrom(
      this.httpService.post<HailUpdateResponse>(
        `v1/hails/${fleetOrder.hailingRequestId}/fleet`,
        {
          status: hailStatus,
          driverPhoneNumber: result.driverPhoneNumber,
          driverLicensePlate: result.driverLicensePlate,
          driverName: result.driverName,
          driverLocation: result.driverLocation,
          meterId: fleetMeter.id,
        },
        {
          headers: {
            "X-API-KEY": this.configService.getOrThrow("CLOUD_TASKS_API_KEY"),
            "x-correlation-id": this.clsService.getContextId(),
          },
        },
      ),
    );

    return response.data;
  }

  async cancelFleetHailingRequest(hailId: string): Promise<void> {
    const response = await firstValueFrom(this.httpService.delete<void>(`v1/hails/${hailId}/fleet`));
    return response.data;
  }

  async cancelHailingRequestByAdmin(
    hailId: string,
    cancellationFee: number,
    cancellationFeeBreakdown: HailingCancellationFeeBreakdown | undefined,
  ): Promise<void> {
    const response = await firstValueFrom(
      this.httpService.put<void>(
        `v1/admin/hails/${hailId}`,
        {
          cancellationFee,
          cancellationFeeBreakdown,
        },
        {
          headers: {
            Authorization: this.clsService.getToken(),
          },
        },
      ),
    );
    return response.data;
  }

  async createHailingOrderRequest(
    body: HailingCreateOrderBody,
    tx: TxHailingRequest,
    tripEstimation: { distanceMeters: number; durationSeconds: number },
    itinerary: HailingItineraryStepResponse[],
    token: string,
    discountIdThirdParty?: string,
    discountRulesThirdParty?: string,
    discountIdDash?: string,
    discountRulesDash?: string,
    fleetVehicle?: FleetVehicleType,
    paymentDetails?: HailingApiPaymentDetailsDto,
  ): Promise<HailingApiCreateOrderResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<HailingApiCreateOrderResponse>(
          "v1/hails",
          {
            platformType: body.platformType,
            fleetPartnerKey: body.fleetPartnerKey,
            fleetVehicleTypeKey: body.fleetVehicleType,
            fleetVehicleType: fleetVehicle,
            fleetQuoteVehicleId: body.fleetQuoteVehicleId,
            txId: tx.id,
            vehiclePreferences: body.fleetVehicleClass ?? { STANDARD: [] },
            time: body.time,
            filters: body.options,
            tripEstimation,
            language: body.language,
            itinerary,
            doubleTunnelFee: body.doubleTunnelFee,
            paymentDetails: paymentDetails,
            operatingAreas: body.operatingAreas,
            applicableDiscounts: {
              discountIdThirdParty: discountIdThirdParty,
              discountRulesThirdParty: discountRulesThirdParty,
              discountIdDash: discountIdDash,
              discountRulesDash: discountRulesDash,
            },
          },
          {
            headers: {
              Authorization: token,
              "x-correlation-id": this.clsService.getContextId(),
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      throw errorBuilder.hailing.createOrderFailed(error);
    }
  }

  async getPriceEstimation(body: HailingCreateOrderBody, token: string): Promise<number> {
    const request = {
      platformType: body.platformType,
      placeIds: body.itinerary.map((itinerary) => itinerary.placeId),
      time: body.time,
      language: body.language,
      sessionToken: body.sessionToken,
      vehiclePreferences: body.fleetVehicleClass,
      fleetVehicleType: body.fleetVehicleType,
      fleetQuoteVehicleId: body.fleetQuoteVehicleId,
      applicableCampaigns: {
        campaignIdThirdParty: body.campaignIdThirdParty,
        campaignIdDash: body.campaignIdDash,
        campaignRulesThirdParty: body.campaignRulesThirdParty,
        campaignRulesDash: body.campaignRulesDash,
      },
      doubleTunnelFee: body.doubleTunnelFee,
    };
    try {
      const response = await firstValueFrom(
        this.httpService.post<HailingApiGetPriceEstimationResponse>("v1/pricing/estimated-total-fee-range", request, {
          headers: {
            Authorization: token,
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );
      return roundUpOneDecimal(response.data.max ?? 0);
    } catch (error) {
      throw errorBuilder.hailing.priceEstimationFailed(error as Error);
    }
  }

  getCreateHailingOrderV3RequestPayload(
    body: HailingCreateOrderBodyV3,
    tx: Tx,
    discount: HailingDiscount,
    tripEstimation: TripEstimation,
    itinerary: HailingItineraryStepResponse[],
    priceRules: PricingRuleEntity[],
    fleetVehicle?: FleetVehicleType,
    paymentTx?: PaymentTx,
  ) {
    const time =
      body.time && dayjs(body.time).isAfter(dayjs()) ? dayjs(body.time).toISOString() : dayjs().toISOString();
    return {
      platformType: body.platformType,
      fleetPartnerKey: body.fleetPartnerKey,
      fleetVehicleTypeKey: body.fleetVehicleType,
      fleetVehicleType: fleetVehicle,
      fleetQuoteVehicleId: body.fleetQuoteVehicleId,
      txId: tx.id,
      isFixedPrice: "isFixedPrice" in tx.metadata ? tx.metadata.isFixedPrice : false,
      preferVehicleClasses: body.preferVehicleClasses ?? ["FOUR_SEATER"],
      time,
      filters: body.options,
      tripEstimation,
      language: body.language,
      itinerary,
      operatingAreas: body.operatingAreas,
      paymentDetails: {
        cardPrefix: paymentTx?.paymentInstrument?.cardPrefix,
        cardSuffix: paymentTx?.paymentInstrument?.cardSuffix,
        cardType: paymentTx?.paymentInstrument?.cardType,
      },
      clientType: body.clientType,
      prioritizeFavoriteDrivers: body.prioritizeFavoriteDrivers,
      isDoubleTunnelFee: body.isDoubleTunnelFee,
      applicableDiscounts: {
        discountIdThirdParty: discount?.discountIdThirdParty,
        discountRulesThirdParty: discount?.discountRulesThirdParty,
        discountIdDash: discount?.discountIdDash,
        discountRulesDash: discount?.discountRulesDash,
      },
      priceRules,
    };
  }

  async createHailingOrderV3(
    body: HailingCreateOrderBodyV3,
    tx: Tx,
    discount: HailingDiscount,
    tripEstimation: TripEstimation,
    itinerary: HailingItineraryStepResponse[],
    priceRules: PricingRuleEntity[],
    fleetVehicle?: FleetVehicleType,
    paymentTx?: PaymentTx,
  ): Promise<HailingApiCreateOrderResponse> {
    try {
      const request = this.getCreateHailingOrderV3RequestPayload(
        body,
        tx,
        discount,
        tripEstimation,
        itinerary,
        priceRules,
        fleetVehicle,
        paymentTx,
      );

      const response = await firstValueFrom(
        this.httpService.post<HailingApiCreateOrderResponse>("v3/hails", request, {
          headers: {
            Authorization: this.clsService.getToken(),
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );
      return response.data;
    } catch (error) {
      throw errorBuilder.hailing.createOrderFailed(error);
    }
  }

  getPriceEstimationV3RequestPayload(
    body: HailingCreateOrderBodyV3,
    itinerary: HailingItineraryStepResponse[],
    discount: HailingDiscount,
    priceRules: PricingRuleEntity[],
    fleetVehicle?: FleetVehicleType,
  ) {
    return {
      platformType: body.platformType,
      placeIds: itinerary.map((itinerary) => itinerary.placeId),
      preferVehicleClasses: body.preferVehicleClasses ?? ["FOUR_SEATER"],
      time: dayjs(body.time).toISOString() ?? new Date(),
      language: body.language,
      sessionToken: body.sessionToken,
      fleetVehicleType: fleetVehicle,
      fleetQuoteVehicleId: body.fleetQuoteVehicleId,
      applicableDiscounts: discount,
      isDoubleTunnelFee: body.isDoubleTunnelFee,
      priceRules,
    };
  }

  async getPriceEstimationV3(
    body: HailingCreateOrderBodyV3,
    itinerary: HailingItineraryStepResponse[],
    discount: HailingDiscount,
    priceRules: PricingRuleEntity[],
    fleetVehicle?: FleetVehicleType,
  ): Promise<HailingApiGetPriceEstimationResponseV2> {
    try {
      const request = this.getPriceEstimationV3RequestPayload(body, itinerary, discount, priceRules, fleetVehicle);

      const response = await firstValueFrom(
        this.httpService.post<HailingApiGetPriceEstimationResponseV2>("v3/pricing/estimated-total-fee-range", request, {
          headers: {
            Authorization: this.clsService.getToken(),
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );

      return response.data;
    } catch (error) {
      throw errorBuilder.hailing.priceEstimationFailed(error);
    }
  }

  async getQuotes(
    body: HailingGetQuoteBodyV3,
    discount: HailingDiscount,
    priceRules: PricingRuleEntity[],
  ): Promise<CreateHailQuoteResponse> {
    const request = {
      platformType: body.platformType,
      placeIds: body.placeIds,
      time: body.time ? dayjs(body.time).toISOString() : dayjs().toISOString(),
      language: body.language,
      fleetVehicleTypeKey: body.fleetVehicleTypeKey,
      fleetPartnerKey: body.fleetPartnerKey,
      isDoubleTunnelFee: body.isDoubleTunnelFee,
      applicableDiscounts: discount,
      priceRules,
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post<CreateHailQuoteResponse>("v3/pricing/quotes", request, {
          headers: {
            Authorization: this.clsService.getToken(),
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );
      return response.data;
    } catch (error) {
      throw errorBuilder.hailing.getQuotesFailed(error);
    }
  }

  async cancelHailingRequest(txId: string) {
    try {
      const response = await firstValueFrom(
        this.httpService.patch(
          `v1/hails/${txId}/riders`,
          {
            status: HailStatus.CANCELLED,
          },
          {
            headers: {
              Authorization: this.clsService.getToken(),
              "x-correlation-id": this.clsService.getContextId(),
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      throw errorBuilder.hailing.cancelOrderFailed(error);
    }
  }

  async getHailingRequest(txId: string) {
    try {
      const response = await firstValueFrom(
        this.httpService.get<HailUpdateResponse>(`v3/hails/${txId}/riders`, {
          headers: {
            Authorization: this.clsService.getToken(),
            "x-correlation-id": this.clsService.getContextId(),
          },
        }),
      );
      return response.data;
    } catch (error) {
      throw errorBuilder.hailing.getOrderFailed(error);
    }
  }
}
