import Tx from "@nest/modules/database/entities/tx.entity";
import { HailingItineraryStepResponse } from "@nest/modules/hailing/dto/hailing.dto";
import { TripEstimation } from "@nest/modules/location/dto/location.dto";
import { HailType, TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { TxPayoutStatus } from "@nest/modules/transaction/dto/txPayoutStatus.dto";
import { isTxHailing, isTxTrip } from "@nest/modules/transaction/dto/tx.dto";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { Billing, Vehicle } from "@nest/modules/appDatabase/documents/trip.document";
import Merchant from "@nest/modules/database/entities/merchant.entity";
import dayjs from "@nest/modules/utils/dayjs";
import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";

import { ClientType, HailingGuestMetaData, PlatformType } from "../../hailing/dto/create-hail.dto";

export interface GetTransactionsResponse {
  items: HailingOrder[];
  total: number;
  limit: number;
}

export interface HailingOrderMetaData {
  status: TxHailingRequestStatus;
  platformType: PlatformType;
  itinerary: HailingItineraryStepResponse[];
  clientType?: ClientType;
  charges?: {
    cancellationFee?: number;
    cancellationFeeBreakdown?: {
      total: number;
      driverPayout: number;
      dashFee: number;
    };
  };
  tripEstimation?: TripEstimation;
  licensePlate?: string;
  discounts: {
    discountIdThirdParty?: string;
    discountRulesThirdParty?: string;
    discountIdDash?: string;
    discountRulesDash?: string;
  };
  billing?: Billing;
  vehicle?: Vehicle;
  guest?: HailingGuestMetaData;
  hailId?: string;
}

export class HailingOrder {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  payoutStatus?: TxPayoutStatus;
  metadata: HailingOrderMetaData;
  merchantId?: string;
  merchant?: Merchant | null;
  payoutMerchantId?: string;
  payoutMerchant?: Merchant | null;
  dashFee: number;
  tripTotal: number;
  discount: number;
  type: TxTypes;
  status: TxHailingRequestStatus;
  platformType: PlatformType;
  clientType?: ClientType;
  time: Date;
  paymentInformation?: PaymentTx;
  preferVehicleClasses?: string[];
  hailType: HailType;
  qrWebReceiptUrl?: string;

  constructor(tx: Tx) {
    this.id = tx.id;
    this.createdAt = tx.createdAt;
    this.updatedAt = tx.updatedAt;
    this.type = tx.type;
    this.userId = tx.userId;
    this.payoutStatus = tx.payoutStatus;
    this.merchantId = tx.merchantId;
    this.payoutMerchantId = tx.payoutMerchantId;
    this.merchant = tx.merchant;
    this.payoutMerchant = tx.payoutMerchant;
    this.paymentInformation = tx.paymentTx?.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())[0];

    if (isTxHailing(tx)) {
      this.tripTotal = tx.metadata.minMaxFareCalculations?.[0].total ?? 0;
      this.dashFee = tx.metadata.minMaxFareCalculations?.[0].dashTransactionFee ?? 0;
      this.discount = tx.metadata.minMaxFareCalculations?.[0].discount ?? 0;
      this.metadata = {
        status: tx.metadata.status,
        platformType: tx.metadata.platformType,
        itinerary: tx.metadata.itinerary,
        charges: tx.metadata.charges,
        tripEstimation: tx.metadata.tripEstimation,
        licensePlate: tx.metadata.licensePlate,
        discounts: tx.metadata.discounts,
        clientType: tx.metadata.clientType,
        guest: tx.metadata.guest,
      };
      this.status = tx.metadata.status;
      this.platformType = tx.metadata.platformType;
      this.clientType = tx.metadata.clientType;
      this.preferVehicleClasses =
        tx.metadata.request && "preferVehicleClasses" in tx.metadata.request
          ? tx.metadata.request.preferVehicleClasses
          : [];
      this.time = dayjs(tx.metadata.time).utc().toDate();
      this.hailType = tx.metadata.type as HailType;
      this.qrWebReceiptUrl = tx.metadata.qrWebReceiptUrl;
    } else if (isTxTrip(tx)) {
      this.tripTotal = tx.total ?? 0;
      this.dashFee = tx.dashFee ?? 0;
      this.discount = tx.discount ?? 0;
      this.metadata = {
        status: TxHailingRequestStatus.COMPLETED,
        hailId: tx.metadata.hailId,
        platformType: tx.metadata.platformType || PlatformType.DASH,
        itinerary: tx.metadata.tripItinerary || [],
        licensePlate: tx.metadata.licensePlate,
        discounts: {
          discountIdThirdParty: tx.metadata.billing?.discountSettings?.discountIdThirdParty,
          discountRulesThirdParty: tx.metadata.billing?.discountSettings?.discountRulesThirdParty,
          discountIdDash: tx.metadata.billing?.discountSettings?.discountIdDash,
          discountRulesDash: tx.metadata.billing?.discountSettings?.discountRulesDash,
        },
        billing: tx.metadata.billing,
        clientType: tx.metadata.clientType,
        vehicle: tx.metadata.vehicle,
      };
      this.status = TxHailingRequestStatus.COMPLETED;
      this.platformType = tx.metadata.platformType || PlatformType.DASH;
      this.clientType = tx.metadata.clientType;
      this.time = tx.metadata.tripStart;
      this.preferVehicleClasses = [tx.metadata.vehicle?.class ?? ""];
      this.hailType = tx.metadata.hailOrderType as HailType;
    }
  }

  toJSON() {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      userId: this.userId,
      payoutStatus: this.payoutStatus,
      metadata: this.metadata,
      merchantId: this.merchantId,
      merchant: this.merchant,
      payoutMerchantId: this.payoutMerchantId,
      payoutMerchant: this.payoutMerchant,
      dashFee: this.dashFee,
      tripTotal: this.tripTotal,
      discount: this.discount,
      status: this.status,
      platformType: this.platformType,
      clientType: this.clientType,
      type: this.type,
      time: this.time,
      paymentInformation: this.paymentInformation,
      preferVehicleClasses: this.preferVehicleClasses,
      hailType: this.hailType,
      qrWebReceiptUrl: this.qrWebReceiptUrl,
    } as HailingOrder;
  }
}
