import { MigrationInterface, QueryRunner } from "typeorm";

export class RenamePriceRules1755240894042 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update all pricing rules with clientType 'HOTEL_SOLUTION' to 'B2B_APP'
        await queryRunner.query(
            "UPDATE \"pricing_rule\" SET \"clientType\" = 'B2B_APP' WHERE \"clientType\" = 'HOTEL_SOLUTION'"
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert all pricing rules with clientType 'B2B_APP' back to 'HOTEL_SOLUTION'
        await queryRunner.query(
            "UPDATE \"pricing_rule\" SET \"clientType\" = 'HOTEL_SOLUTION' WHERE \"clientType\" = 'B2B_APP'"
        );
    }

}
