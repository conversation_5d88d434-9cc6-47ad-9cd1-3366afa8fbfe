import { Injectable } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";

import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { isTxHailing } from "@nest/modules/transaction/dto/tx.dto";
import { HailType, TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { TransactionEventService } from "@nest/modules/transaction/modules/transactionEvent.service";
import { TxEventType } from "@nest/modules/transaction/dto/txEventType.dto";
import { LogAll } from "@nest/decorators/log-all.decorator";
import dayjs from "@nest/modules/utils/dayjs";

import { HailingOrder } from "../../meTx/dto/me-tx.dto";

@LogAll()
@Injectable()
export class CancelHailingRequestProcessor {
  constructor(
    private readonly txRepository: TxRepository,
    private readonly hailingApiService: HailingApiService,
    private readonly clsService: ClsContextStorageService,
    private readonly transactionEventService: TransactionEventService,
  ) {}

  async execute(txId: string, user: DecodedIdToken, token: string): Promise<HailingOrder | null> {
    this.clsService.setToken(token);

    const tx = await this.txRepository.findOne({
      where: { id: txId, type: TxTypes.HAILING_REQUEST },
    });

    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    if (!isTxHailing(tx)) {
      return new HailingOrder(tx).toJSON();
    }

    if ([TxHailingRequestStatus.CANCELLED, TxHailingRequestStatus.COMPLETED].includes(tx.metadata.status)) {
      return new HailingOrder(tx).toJSON();
    }

    if (tx.metadata.status === TxHailingRequestStatus.PENDING_PAYMENT) {
      await this.transactionEventService.addEvent(txId, user.uid, {
        type: TxEventType.HAILING_USER_CANCELS_ORDER,
      });
      const newTx = await this.txRepository.findOne({
        where: { id: txId },
      });
      if (!newTx) {
        throw errorBuilder.transaction.notFound(txId);
      }
      return new HailingOrder(newTx).toJSON();
    }

    // IMPORTANT: if order is already timeout should just cancel order only
    if (dayjs(tx.metadata.time).isBefore(dayjs()) && tx.metadata.type === HailType.SCHEDULED) {
      await this.transactionEventService.addEvent(txId, user.uid, {
        type: TxEventType.HAILING_USER_CANCELS_ORDER,
      });
      const newTx = await this.txRepository.findOne({
        where: { id: txId },
      });
      if (!newTx) {
        throw errorBuilder.transaction.notFound(txId);
      }
      return new HailingOrder(newTx).toJSON();
    }

    await this.hailingApiService.cancelHailingRequest(txId);

    return new HailingOrder(tx).toJSON();
  }
}
