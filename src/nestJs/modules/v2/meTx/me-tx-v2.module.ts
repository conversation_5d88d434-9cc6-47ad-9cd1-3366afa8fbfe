import { <PERSON>du<PERSON> } from "@nestjs/common";

import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";

import { MeTxV2Service } from "./services/me-tx-v2.service";
import { MeTxV2Controller } from "./me-tx-v2.controller";

@Module({
  providers: [TxRepository, PaymentTxRepository, MeTxV2Service, UserRepository],
  imports: [],
  controllers: [MeTxV2Controller],
  exports: [MeTxV2Service],
})
export class MeTxV2Module {}
