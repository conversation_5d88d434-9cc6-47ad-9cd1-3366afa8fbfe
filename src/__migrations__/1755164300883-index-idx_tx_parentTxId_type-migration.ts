import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1755164300883 implements MigrationInterface {
    name = "Migration1755164300883"

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query("CREATE INDEX CONCURRENTLY \"idx_tx_parentTxId_type\" ON \"tx\" (\"type\", \"parentTxId\" DESC NULLS LAST)");
}

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query("DROP INDEX \"idx_tx_parentTxId_type\"");
    }

    public get transaction(): boolean {
        return false;
    }
}
