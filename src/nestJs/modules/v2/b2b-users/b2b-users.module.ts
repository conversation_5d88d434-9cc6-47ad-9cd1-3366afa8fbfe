import { <PERSON>du<PERSON> } from "@nestjs/common";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { FirebaseAuthModule } from "@nest/modules/v2/firebase-auth/firebase-auth.module";
import { OrganizationRepository } from "@nest/modules/database/repositories/organization.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";

import { B2bUserController } from "./b2b-user.controller";
import { B2bUsersService } from "./services/b2b-users.service";

@Module({
  imports: [AppDatabaseModule, FirebaseAuthModule],
  providers: [B2bUsersService, UserRepository, OrganizationRepository],
  controllers: [B2bUserController],
  exports: [],
})
export class B2bUsersModule {}
