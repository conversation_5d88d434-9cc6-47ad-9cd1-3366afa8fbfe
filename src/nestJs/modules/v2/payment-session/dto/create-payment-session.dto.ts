import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class CreatePaymentSessionRequestDto {
  @ApiProperty({
    description: "The ID of the transaction",
    example: "tx-123",
  })
  txId: string;

  @ApiProperty({
    description: "the ID of the user",
    example: "user-123",
  })
  userId: string;

  @ApiProperty({
    description: "The amount for the payment",
    example: 100,
  })
  amount: number;

  @ApiProperty({
    description: "The currency for the payment",
    example: "HKD",
  })
  currency: string;
}

export const createPaymentSessionSchema = Joi.object<CreatePaymentSessionRequestDto>({
  txId: Joi.string().required(),
  userId: Joi.string().required(),
  amount: Joi.number().positive().min(1).precision(1).required(),
  currency: Joi.string().valid("HKD").length(3).required(),
});

export class CreatePaymentSessionResponseDto {
  @ApiProperty({
    description: "The signed payment token",
  })
  token: string;

  @ApiProperty({
    description: "The unique session ID",
  })
  sessionId: string;

  @ApiProperty({
    description: "Token validity in seconds",
  })
  validUntil: number;

  @ApiProperty({
    description: "The request payload for the payment session",
  })
  request: CreatePaymentSessionRequestDto;
}
