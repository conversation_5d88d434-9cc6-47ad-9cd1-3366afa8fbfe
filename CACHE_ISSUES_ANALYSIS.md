# Redis Cache Issues and Solutions

## Problems Identified

### 1. **Cache Stale Data Issues**
- **Root Cause**: Multiple Redis connections without proper namespacing
- **Symptom**: Cache returns previous versions of data
- **Solution**: Added namespace `"dash-cache"` to prevent key conflicts

### 2. **Connection Management**
- **Root Cause**: No error handling for Redis connection failures
- **Symptom**: Cache operations silently fail, leading to inconsistent behavior
- **Solution**: Added error handlers and graceful degradation

### 3. **Race Conditions**
- **Root Cause**: Multiple concurrent requests accessing cache simultaneously
- **Symptom**: Inconsistent cache state, sometimes returning stale data
- **Solution**: Implemented explicit get/set pattern instead of wrap() in critical paths

### 4. **TTL Configuration Inconsistencies**
- **Root Cause**: Different TTL values in different parts of the codebase
- **Symptom**: Unpredictable cache expiration behavior
- **Solution**: Centralized TTL configuration with sensible defaults

## Key Changes Made

### 1. Enhanced Cache Module (`cache.module.ts`)
```typescript
// Added namespace to prevent key conflicts
const keyvStore = createKeyv(redisUrl, {
  namespace: "dash-cache",
});

// Added error handling
keyvStore.on("error", (err) => {
  console.error("Redis cache connection error:", err);
});

// Fixed TTL default (5 minutes instead of 1 second)
const ttl = parseInt(configService.get("CACHE_TTL") || "300000", 10);
```

### 2. Enhanced Cache Service (`cache.service.ts`)
```typescript
// Added error handling for all cache operations
async get<T>(key: string): Promise<T | undefined> {
  try {
    return this.cacheManager.get(key);
  } catch (error) {
    console.error(`Cache get error for key ${key}:`, error);
    return undefined; // Graceful degradation
  }
}

// Added versioned keys to prevent stale data
getVersionedKey(key: string, version?: string): string {
  const appVersion = process.env.APP_VERSION || "1.0.0";
  const keyVersion = version || appVersion;
  return `v${keyVersion}:${key}`;
}

// Added health check method
async healthCheck(): Promise<boolean> {
  // Test cache connectivity and functionality
}
```

## Best Practices for Cache Usage

### 1. **Use Explicit Get/Set Pattern for Critical Data**
```typescript
// ❌ Avoid wrap() for race-sensitive operations
const data = await cacheService.wrap(key, fetchFunction);

// ✅ Use explicit get/set pattern
let data = await cacheService.get<DataType>(key);
if (!data) {
  data = await fetchFromDatabase();
  await cacheService.set(key, data, ttl);
}
```

### 2. **Always Handle Cache Failures Gracefully**
```typescript
// ✅ Cache should never break your application
try {
  const cached = await cacheService.get(key);
  if (cached) return cached;
} catch (error) {
  console.error('Cache error, falling back to database:', error);
}
// Always have a fallback to database
const data = await database.find(id);
```

### 3. **Use Versioned Keys for Data Consistency**
```typescript
// ✅ Include version in cache keys
const cacheKey = cacheService.getVersionedKey(`user:${userId}`);
```

## Monitoring and Debugging

### 1. **Add Cache Metrics**
```typescript
// Check cache health
const isHealthy = await cacheService.healthCheck();
if (!isHealthy) {
  // Log alert or metric
}
```

### 2. **Environment-Specific Cache Keys**
The namespace "dash-cache" ensures different environments don't interfere:
- Development: `dash-cache:v1.0.0:PUBLIC_HAILING_REQUEST:123`
- Production: `dash-cache:v1.0.0:PUBLIC_HAILING_REQUEST:123`

### 3. **Connection Monitoring**
Error events are now logged for Redis connection issues.

## Configuration Recommendations

### 1. **Environment Variables**
```bash
# Use consistent TTL (5 minutes = 300000ms)
CACHE_TTL=300000

# Ensure Redis URL includes database number
REDIS_CACHE_URL=redis://127.0.0.1:6379/3

# Add app version for cache versioning
APP_VERSION=1.0.0
```

### 2. **Redis Database Separation**
- Database 2: PubSub operations
- Database 3: Application cache
- This prevents cross-contamination

## Testing the Fix

### 1. **Verify Cache Namespace**
```bash
# Connect to Redis and check keys
redis-cli -n 3
KEYS dash-cache:*
```

### 2. **Test Cache Health**
```typescript
// Add to your health check endpoint
const cacheHealth = await cacheService.healthCheck();
```

### 3. **Monitor for Stale Data**
Look for logs indicating cache errors or fallbacks to database queries.

## Migration Notes

- Existing cache keys without namespace will be ignored (they'll expire naturally)
- New keys will use the "dash-cache" namespace
- TTL changes from 1 second to 5 minutes by default
- All cache operations now have error handling

This should resolve the issue of cache returning previous versions by:
1. Preventing key conflicts with namespacing
2. Adding proper error handling and fallbacks
3. Using versioned keys for consistency
4. Improving connection management
