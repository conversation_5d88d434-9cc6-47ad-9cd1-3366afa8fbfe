import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1753148234847 implements MigrationInterface {
  name = "1753148234847";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "merchant_campaign" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(100) NOT NULL, "startAt" TIMESTAMP NOT NULL, "endAt" TIMESTAMP NOT NULL, "applicationRules" character varying NOT NULL, "bonusAmount" integer NOT NULL DEFAULT \'0\', "priority" integer NOT NULL DEFAULT \'0\', CONSTRAINT "PK_a9cc6fa1436899b336f886fca1c" PRIMARY KEY ("id"))',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE "merchant_campaign"');
  }
}
