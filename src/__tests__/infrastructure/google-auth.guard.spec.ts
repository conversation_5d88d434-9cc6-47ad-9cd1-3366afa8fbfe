import { GoogleAuthGuard } from "../../nestJs/infrastructure/guards/google-auth.guard";
import { UnauthorizedException, ExecutionContext } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { OAuth2Client } from "google-auth-library";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { Test, TestingModule } from "@nestjs/testing";

jest.mock("google-auth-library")
jest.mock("@nestjs/config");
jest.mock("@nest/modules/utils/logger/logger.service");

describe("GoogleAuthGuard", () => {
    let guard: GoogleAuthGuard;
    let configService: ConfigService;
    let googleClient: OAuth2Client;
    let logger: LoggerServiceAdapter;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ConfigService,
                OAuth2Client,
                LoggerServiceAdapter,
                GoogleAuthGuard,
            ],
        }).compile();

        guard = module.get(GoogleAuthGuard);
        configService = module.get(ConfigService);
        googleClient = module.get(OAuth2Client);
        logger = module.get(LoggerServiceAdapter);

        jest.clearAllMocks();
    });

    it("should be defined", () => {
        expect(guard).toBeDefined();
        expect(configService).toBeDefined();
        expect(googleClient).toBeDefined();
        expect(logger).toBeDefined();
    });

    function createContextWithHeader(header?: string) {
        const req: any = {
            headers: header ? { Authorization: header } : {},
        };
        return {
            switchToHttp: () => ({
                getRequest: () => req,
            }),
        } as unknown as ExecutionContext;
    }

    it("should throw UnauthorizedException if Authorization header is missing", async () => {
        await expect(guard.canActivate(createContextWithHeader()))
            .rejects
            .toThrow(UnauthorizedException);
    });

    it("should throw UnauthorizedException if header format is invalid", async () => {
        await expect(guard.canActivate(createContextWithHeader("InvalidTokenFormat")))
            .rejects
            .toThrow(UnauthorizedException);
    });

    it("should throw UnauthorizedException if scheme is not Bearer", async () => {
        await expect(guard.canActivate(createContextWithHeader("Basic sometoken")))
            .rejects
            .toThrow(UnauthorizedException);
    });

    it("should throw UnauthorizedException if verifyIdToken fails", async () => {
        (googleClient.verifyIdToken as jest.Mock).mockRejectedValue(new Error("Invalid token"));

        await expect(guard.canActivate(createContextWithHeader("Bearer sometoken")))
            .rejects
            .toThrow(UnauthorizedException);

        expect(logger.error).toHaveBeenCalledWith(expect.stringContaining("Internal token validation failed"));
    });

    it("should throw UnauthorizedException if payload is missing", async () => {
        (googleClient.verifyIdToken as jest.Mock).mockResolvedValue({
            getPayload: () => null,
        });

        await expect(guard.canActivate(createContextWithHeader("Bearer sometoken")))
            .rejects
            .toThrow(Error);
    });

    it("should attach payload to request and return true if token is valid", async () => {
        const mockPayload = { sub: "123", email: "<EMAIL>" };
        (googleClient.verifyIdToken as jest.Mock).mockResolvedValue({
            getPayload: () => mockPayload,
        });
        const context = createContextWithHeader("Bearer sometoken");

        const result = await guard.canActivate(context);
        const req = context.switchToHttp().getRequest();

        expect(result).toBe(true);
        expect(req.internalIdentity).toEqual(mockPayload);
    });

    it("should use audience from configService", async () => {
        const context = createContextWithHeader("Bearer sometoken");

        await guard.canActivate(context);

        expect(configService.get).toHaveBeenCalledWith("GOOGLE_CLIENT_ID", "BACKEND_FUNCTIONS");
    });
});