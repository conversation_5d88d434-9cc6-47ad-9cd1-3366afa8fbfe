import { Global, Module } from "@nestjs/common";
import { createKeyv } from "@keyv/redis";
import { Cacheable } from "cacheable";
import { ConfigModule, ConfigService } from "@nestjs/config";

import { CACHE_TOKEN, CacheService } from "./cache.service";
@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: CACHE_TOKEN,
      useFactory: async (configService: ConfigService) => {
        const redisUrl = configService.get("REDIS_CACHE_URL");
        const cacheTtl = configService.get("CACHE_TTL");

        if (!redisUrl) {
          console.warn("REDIS_CACHE_URL not configured, using in-memory cache");
          return new Cacheable({
            ttl: cacheTtl ? parseInt(cacheTtl, 10) : 300000,
          });
        }

        try {
          const primary = createKeyv(redisUrl, {
            namespace: "backend-function-cache",
            keyPrefixSeparator: ":",
            noNamespaceAffectsAll: true,
          });

          return new Cacheable({
            primary,
            ttl: cacheTtl ? parseInt(cacheTtl, 10) : 300000, // default 5 minutes
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.error("❌ Redis cache connection error:", errorMessage);
          console.error("Full error:", error);
          console.warn("🔄 Falling back to in-memory cache");

          // Fallback to in-memory cache if Redis is not available
          return new Cacheable({
            ttl: cacheTtl ? parseInt(cacheTtl, 10) : 300000,
          });
        }
      },
      inject: [ConfigService],
    },
    CacheService,
  ],
  exports: [CacheService],
})
export class RedisCacheModule {}
