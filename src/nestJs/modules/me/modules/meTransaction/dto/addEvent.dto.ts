import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { AddEventBodySystem } from "../../../../system/modules/systemTransaction/dto/addEvent.dto";
import {
  HailingAdminCancelsOrderEventContent,
  HailingMerchantAcceptsOrderEventContent,
  HailingMerchantPickUpConfirmedOrderEventContent,
  TxEventType,
} from "../../../../transaction/dto/txEventType.dto";

export type AddEventBodyMe =
  | AddEventBodyDefault
  | AddEventBodyHailingAdminCancelsOrder
  | AddEventBodyHailingMerchantAcceptsOrder
  | AddEventBodyHaildingMerchantConfirmsPickUpOrder
  | AddEventBodyHailingUserUpdatesOrder;

export type AddEventBody = AddEventBodyMe | AddEventBodySystem;

export class AddEventBodyDefault {
  @ApiProperty({ description: "Tx event type", enum: TxEventType, example: TxEventType.HAILING_USER_CREATES_ORDER })
  type:
    | TxEventType.HAILING_MERCHANT_CANCELS_ORDER
    | TxEventType.HAILING_USER_CANCELS_ORDER
    | TxEventType.HAILING_USER_CREATES_ORDER
    | TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION
    | TxEventType.HAILING_ORDER_COMPLETED
    | TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION;
}

export class AddEventBodyHailingMerchantAcceptsOrder {
  @ApiProperty({
    description: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
    type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
    example: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
  })
  type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;

  @ApiProperty({ description: "Tx event content" })
  content?: HailingMerchantAcceptsOrderEventContent;
}

export class AddEventBodyHaildingMerchantConfirmsPickUpOrder {
  @ApiProperty({
    description: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
    type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
    example: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
  })
  type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED;

  @ApiProperty({ description: "Tx event content" })
  content?: HailingMerchantPickUpConfirmedOrderEventContent;
}

export class AddEventBodyHailingUserUpdatesOrder {
  @ApiProperty({
    description: TxEventType.HAILING_USER_UPDATES_ORDER,
    type: TxEventType.HAILING_USER_UPDATES_ORDER,
    example: TxEventType.HAILING_USER_UPDATES_ORDER,
  })
  type: TxEventType.HAILING_USER_UPDATES_ORDER;

  @ApiProperty({
    description: "Order update data",
    example: { boostAmount: 150, minMaxFareCalculations: { boost: 150 } },
    required: false,
  })
  content?: Record<string, any>;
}

export class AddEventBodyHailingAdminCancelsOrder {
  @ApiProperty({
    description: TxEventType.HAILING_ADMIN_CANCELS_ORDER,
    type: TxEventType.HAILING_ADMIN_CANCELS_ORDER,
    example: TxEventType.HAILING_ADMIN_CANCELS_ORDER,
  })
  type: TxEventType.HAILING_ADMIN_CANCELS_ORDER;

  @ApiProperty({ description: "Tx event content" })
  content: HailingAdminCancelsOrderEventContent;
}

export const addEventBodySchemaMe = Joi.alternatives([
  Joi.object<AddEventBodyHailingMerchantAcceptsOrder>({
    type: Joi.string().valid(TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER).required(),
    content: Joi.object<HailingMerchantAcceptsOrderEventContent>({
      phoneNumber: Joi.string().required(),
      heartBeatOnAccepted: Joi.object({
        lat: Joi.number().required(),
        lng: Joi.number().required(),
        heading: Joi.number().allow(null),
        speed: Joi.number().allow(null),
      }).optional(),
      meter: Joi.string().required(),
      distance: Joi.number().allow(null),
      eta: Joi.number().allow(null),
      boostAmount: Joi.number().allow(null),
      bonusAmount: Joi.number().allow(null),
    }).required(),
  }),
  Joi.object<AddEventBodyHaildingMerchantConfirmsPickUpOrder>({
    type: Joi.string().valid(TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED).required(),
    content: Joi.object<HailingMerchantPickUpConfirmedOrderEventContent>({
      txId: Joi.string().required(),
      meterId: Joi.string().required(),
    }).required(),
  }),
  Joi.object<AddEventBodyHailingUserUpdatesOrder>({
    type: Joi.string().valid(TxEventType.HAILING_USER_UPDATES_ORDER).required(),
    content: Joi.object().optional(),
  }),
  Joi.object<AddEventBodyMe>({
    type: Joi.string()
      .valid(
        TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
        TxEventType.HAILING_USER_CANCELS_ORDER,
        TxEventType.HAILING_USER_CREATES_ORDER,
        TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION,
        TxEventType.HAILING_ORDER_COMPLETED,
        TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION,
      )
      .required(),
  }),
  Joi.object<AddEventBodySystem>({
    type: Joi.string().valid(TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT).required(),
  }),
  Joi.object<AddEventBodyHailingAdminCancelsOrder>({
    type: Joi.string().valid(TxEventType.HAILING_ADMIN_CANCELS_ORDER).required(),
    content: Joi.object().optional(),
  }),
]);

export enum TxEventCreatedBy {
  SYSTEM = "SYSTEM",
}
