import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTypeormIndexForTxMetadataClientType1753949564243 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('CREATE INDEX CONCURRENTLY "idx_tx_metadata_clientType" ON "tx" ((metadata->>\'clientType\'))');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX CONCURRENTLY "idx_tx_metadata_clientType"');
    }

    public get transaction(): boolean {
        return false;
    }
}
