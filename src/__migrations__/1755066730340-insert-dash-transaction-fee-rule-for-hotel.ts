import { MigrationInterface, QueryRunner } from "typeorm";

export class InsertDashTransactionFeeRuleForHotel1755066730340 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query(
            `INSERT INTO "pricing_rule" ("platformType", "clientType", "orderType", "jsonRule", "startAt", "endAt", "fareType") 
             VALUES ('DASH', 'HOTEL_SOLUTION', 'LIVE', '{ "+": [ 10, { "*": [ 0.10, { "var": "fare" } ] } ] }', '2025-01-01 00:00:00', '2025-12-31 23:59:59', 'DASH_TRANSACTION_FEE')`
        );
        queryRunner.query(
            `INSERT INTO "pricing_rule" ("platformType", "clientType", "orderType", "jsonRule", "startAt", "endAt", "fareType") 
             VALUES ('DASH', 'HOTEL_SOLUTION', 'SCHEDULED', '{ "+": [ 10, { "*": [ 0.10, { "var": "fare" } ] } ] }', '2025-01-01 00:00:00', '2025-12-31 23:59:59', 'DASH_TRANSACTION_FEE')`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query('DELETE FROM "pricing_rule" WHERE "platformType" = \'DASH\' AND "clientType" = \'HOTEL_SOLUTION\' AND "orderType" = \'LIVE\' AND "fareType" = \'DASH_TRANSACTION_FEE\'');
        queryRunner.query('DELETE FROM "pricing_rule" WHERE "platformType" = \'DASH\' AND "clientType" = \'HOTEL_SOLUTION\' AND "orderType" = \'SCHEDULED\' AND "fareType" = \'DASH_TRANSACTION_FEE\'');
    }

}
