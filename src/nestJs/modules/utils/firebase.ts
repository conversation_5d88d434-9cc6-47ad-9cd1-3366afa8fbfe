import { DocumentReference, GeoPoint, Timestamp } from "firebase-admin/firestore";

// Type for data that might have a toJSON method (like Firestore document snapshots)
type SerializableData = 
  | null 
  | undefined 
  | string 
  | number 
  | boolean 
  | Date 
  | Timestamp 
  | GeoPoint 
  | DocumentReference 
  | { toJSON(): unknown }
  | SerializableData[] 
  | { [key: string]: SerializableData };

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const serializeFirestoreData = (data: SerializableData): any => {
  if (data == null) return null;
  
  // Handle Firestore event data with toJ<PERSON>N method
  if (data && typeof data === "object" && "toJSON" in data && typeof data.toJSON === "function") {
    return serializeFirestoreData(data.toJSON() as SerializableData);
  }
  
  if (data instanceof Timestamp) {
    return data.toDate().toISOString();
  }
  if (data instanceof GeoPoint) {
    return { latitude: data.latitude, longitude: data.longitude };
  }
  if (data instanceof DocumentReference) {
    return data.path;
  }
  if (Array.isArray(data)) {
    return data.map(serializeFirestoreData);
  }
  if (typeof data === "object" && data !== null) {
    const result: Record<string, unknown> = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = serializeFirestoreData((data as Record<string, SerializableData>)[key]);
      }
    }
    return result;
  }
  return data;
};
