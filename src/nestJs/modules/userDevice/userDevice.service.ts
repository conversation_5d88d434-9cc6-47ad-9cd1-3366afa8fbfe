import { Inject, Injectable } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { lastValueFrom } from "rxjs";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { ConfigService } from "@nestjs/config";
import { Cache } from "cache-manager";

import { Log } from "../../decorators/log.decorator";
import { UserDeviceRepository } from "../database/repositories/userDevice.repository";
import { PublishMessageForUserDeviceInfoParams } from "../pubsub/dto/publishMessageForUserDeviceInfoParams.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import UserDeviceEntity from "../database/entities/userDevice.entity";

type IpAddressInfo = {
  status: string;
  message?: string;
  country?: string;
  proxy?: boolean;
}

@Injectable()
export class UserDeviceService {
  constructor(
    private readonly userDeviceRepository: UserDeviceRepository,
    private readonly httpService: HttpService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly configService: ConfigService,
  ) { }

  @Log()
  async processDeviceInfo(deviceInfo: PublishMessageForUserDeviceInfoParams): Promise<void> {
    const userDeviceEntity = new UserDeviceEntity()
    userDeviceEntity.userId = deviceInfo.userId;
    userDeviceEntity.deviceId = deviceInfo.deviceId;
    userDeviceEntity.createdAt = new Date(deviceInfo.time);

    const ipAddressInfo = deviceInfo.ipAddress ? await this.getIpAddressInfo(deviceInfo.ipAddress) : null;

    if (ipAddressInfo) {
      userDeviceEntity.ipAddress = deviceInfo.ipAddress;
      userDeviceEntity.ipResolvedCountry = ipAddressInfo.country;
      userDeviceEntity.ipIsProxy = ipAddressInfo.proxy || false;
    }

    await this.userDeviceRepository.upsertUserDevice(userDeviceEntity);
  }

  @Log()
  private async getIpAddressInfo(ipAddress: string): Promise<IpAddressInfo | null> {
    const baseUrl = this.configService.get<string>("IP_LOOKUP_API_BASE_URL") || "http://ip-api.com";
    const ipApiFields = ["status", "message", "country", "proxy"];
    const url = `${baseUrl}/json/${ipAddress}?fields=${ipApiFields.join(",")}`;

    return this.cacheManager.wrap<IpAddressInfo | null>(`${ipAddress}`, async () => {
      try {
        const response = await lastValueFrom(this.httpService.get(url));
        return response.data;
      } catch (error) {
        this.logger.error(`Failed to fetch IP address info for ${ipAddress}: ${error}`);
        return null;
      }
    });
  }
}