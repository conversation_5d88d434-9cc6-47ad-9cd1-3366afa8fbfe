import { Injectable, Inject } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import LoggerServiceAdapter from "../utils/logger/logger.service";
import { UserNotificationFactory } from "../notification/user-notification.factory";
import { NotificationTaskRepository } from "../database/repositories/notificationTask.repository";
import NotificationTask, { NotificationTaskStatusError, NotificationTaskType } from "../database/entities/notificationTask.entity";
import { NotificationSenderRequestPayloadV2, notificationSenderRequestSchemaV2 } from "../notification/notification.interface";
import { ValidationService } from "../validation/validation.service";
import { CloudTaskClientService } from "../cloud-task-client/cloud-task-client.service";
import { errorBuilder } from "../utils/utils/error.utils";

import { InboxCreatorService } from "./inbox-creator.service";
import { RecipientNotificationProcessorService } from "./recipient-notification-processesor.service";

@Injectable()
export class NotificationHandlerV2Service {
  private readonly batchSize: number;
  private readonly maxRetryCount: number;
  constructor(
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
    private readonly userNotificationFactory: UserNotificationFactory,
    private readonly notificationTaskRepository: NotificationTaskRepository,
    private readonly inboxCreatorService: InboxCreatorService,
    private readonly recipientProcessor: RecipientNotificationProcessorService,
    private readonly cloudTaskClientService: CloudTaskClientService,
  ) {
    this.batchSize = parseInt(this.configService.get<string>("NOTIFICATIONS_BATCH_SIZE") || "50", 10);
    this.maxRetryCount = parseInt(this.configService.get<string>("NOTIFICATIONS_MAX_RETRY_COUNT") || "3", 10);
  }

  async handleV2Notification(task: NotificationTask, batchNumber: number, retryCount: number): Promise<NotificationTask> {
    const taskId = task.id;
    this.logger.info(`[V2NotificationHandler] Processing task ${taskId} with batch ${batchNumber}`);

    try {
      task.process();
      await this.notificationTaskRepository.save(task);
    } catch (err) {
      if (err instanceof NotificationTaskStatusError) {
        this.logger.warn(
          `[V2NotificationHandler] Task ${taskId} is already in process or completed, skipping.`,
          { task },
        );
        return task;
      }

      throw err;
    }

    try {
      const notificationSender = this.userNotificationFactory.getSender(task.userType);
      const { scheduledAt: _, updatedBy: __, ...taskPayload } = task.payload;

      const [validPayload] = ValidationService.validate<NotificationSenderRequestPayloadV2[]>([
        { schema: notificationSenderRequestSchemaV2, value: taskPayload },
      ], true);

      if (!validPayload.recipients || validPayload.recipients.length === 0) {
        return task
      }
      const batchStart = batchNumber * this.batchSize;
      const batchEnd = batchStart + this.batchSize;
      const recipientsBatch = validPayload.recipients.slice(batchStart, batchEnd);

      this.logger.info(
        `[V2NotificationHandler] Processing task ${taskId} with batch number ${batchNumber}`,
        {
          taskId,
          batchStart,
          batchEnd,
          batchNumber,
          recipientsCount: validPayload.recipients.length,
          batchSize: recipientsBatch.length,
        },
      );

      if (recipientsBatch.length > 0) {
        const validationResult = this.recipientProcessor.validateRecipientsForTemplates(
          recipientsBatch,
          validPayload.notificationRequest
        );

        if (!validationResult.isValid) {
          const reason = `Template validation failed: Missing variables - ${validationResult.missingVariables.join(", ")}`;
          this.logger.error(`[V2NotificationHandler] ${reason}`);

          task.fail(reason);
          await this.notificationTaskRepository.save(task);
          return task;
        }

        const phoneNumbers = recipientsBatch.map(recipient => recipient.phone).filter(phone => phone) || [];
        this.logger.info(`[V2NotificationHandler] Extracted ${phoneNumbers.length} phone numbers from batch ${batchNumber}`);

        const userTokens = await notificationSender.getValidPushNotificationTokens(task.id, phoneNumbers);

        const batchPayload = { ...validPayload, recipients: recipientsBatch };
        await this.recipientProcessor.processRecipientsWithTemplates(task, batchPayload, userTokens);

        if (task.type === NotificationTaskType.PUSH_INBOX || task.type === NotificationTaskType.INBOX) {
          this.logger.info(`[V2NotificationHandler] Creating inbox messages for task ${taskId} (batch ${batchNumber})`);
          await this.inboxCreatorService.createInboxMessages(task, batchPayload);
        }

        if (batchEnd < validPayload.recipients.length) {
          task.schedule();
          await this.notificationTaskRepository.save(task);
          await this.scheduleNextBatchNotificationTask(taskId, batchNumber);

          return task;
        }
      }


      task.complete();
      await this.notificationTaskRepository.save(task);
      this.logger.info(`[V2NotificationHandler] ✅ Successfully completed task ${taskId}`);
      return task;
    } catch (error) {
      this.logger.error(
        "[V2NotificationHandler] Error handling V2 notification task",
        { taskId: task.id },
        error as Error,
      );

      if (retryCount >= this.maxRetryCount) {
        this.logger.error(
          "[V2NotificationHandler] Max retry count reached for the task. Marking as failed.",
          { taskId, retryCount },
        );
        task.fail((error as Error).message);
        await this.notificationTaskRepository.save(task);
        return task;
      }

      task.schedule();
      await this.notificationTaskRepository.save(task);
      throw errorBuilder.notification.errorSending();
    }
  }

  private async scheduleNextBatchNotificationTask(notificationTaskId: string, currentBatchNumber: number): Promise<string> {
    return this.cloudTaskClientService.enqueueSendNotificationTaskBatch(notificationTaskId, currentBatchNumber + 1);
  }
}