import { CloudTasksClient } from "@google-cloud/tasks";
import { google } from "@google-cloud/tasks/build/protos/protos";
import { Injectable, Inject } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import LoggerServiceAdapter from "../utils/logger/logger.service";

import { CloudTaskQueueName, CloudTaskNameUrlMap, CloudTaskName } from "./cloud-task-client.enum";

@Injectable()
export class CloudTaskClientService {
  private client: CloudTasksClient;
  private projectId: string;
  private location: string;
  private apiKey: string;

  constructor(
    private readonly configService: ConfigService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {
    this.client = new CloudTasksClient();
    this.projectId = this.configService.getOrThrow<string>("GCLOUD_PROJECT");
    this.location = this.configService.getOrThrow<string>("CLOUD_TASKS_LOCATION") || "asia-east2";
    this.apiKey = this.configService.getOrThrow<string>("CLOUD_TASKS_API_KEY");
  }

  /**
   * Enqueues a task to be executed immediately.
   * @param queueName Name of the queue
   * @param url URL to send the task to
   * @param payload Data to send with the task
   * @returns Name of the task
   */
  async enqueueHttpTaskNow<T>(queueName: CloudTaskQueueName, url: string, payload?: T) {
    return this.enqueueHttpTask<T>(queueName, url, payload);
  }

  /**
   * Enqueues a task to be executed at a later time.
   * @param queueName Name of the queue
   * @param url URL to send the task to
   * @param payload Data to send with the task
   * @param delayInSeconds Optional amount of time in seconds to delay the task. If not provided, the task is executed immediately.
   * @returns Name of the task
   */
  async enqueueHttpTaskWithDelay<T>(
    queueName: CloudTaskQueueName,
    url: string,
    payload: T,
    delayInSeconds?: number,
  ): Promise<string | undefined | null> {
    let scheduleTimeInSeconds: number | undefined;
    if (delayInSeconds) {
      scheduleTimeInSeconds = Date.now() / 1000 + delayInSeconds;
    }

    return this.enqueueHttpTask<T>(queueName, url, payload, scheduleTimeInSeconds);
  }

  /**
   * Enqueues a task to be executed at a later time.
   * @param queueName Name of the queue
   * @param url URL to send the task to
   * @param payload Data to send with the task
   * @param scheduleTimeInSeconds Optional amount of time in seconds to delay the task. If not provided, the task is executed immediately.
   * @returns Name of the task
   */
  async enqueueHttpTask<T>(
    queueName: CloudTaskQueueName,
    url: string,
    payload?: T,
    scheduleTimeInSeconds?: number,
  ): Promise<string> {
    this.logger.info("CloudTaskClientService/enqueueHttpTask-start", {
      queueName,
      url,
      payload,
      scheduleTimeInSeconds,
    });
    const parent = this.client.queuePath(this.projectId, this.location, queueName);

    const task: google.cloud.tasks.v2.ITask = {
      httpRequest: {
        httpMethod: "POST" as const,
        url,
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": this.apiKey,
        },
        body: payload ? Buffer.from(JSON.stringify(payload)).toString("base64") : null,
      },
    };

    if (scheduleTimeInSeconds) {
      task.scheduleTime = {
        seconds: scheduleTimeInSeconds,
      };
    }
    try {
      const [response] = await this.client.createTask({ parent, task });
      this.logger.info("CloudTaskClientService/enqueueHttpTask-end", {
        taskName: response.name,
        response,
        queueName,
        url,
        payload,
        scheduleTimeInSeconds,
      });
      return response.name!;
    } catch (error) {
      this.logger.error("CloudTaskClientService/enqueueHttpTask-end", { queueName, url }, error as Error);
      throw error;
    }
  }

  /**
   * Custom methods for each client or use-case can be added below
   */

  /**
   * Enqueues a task to send a notification.
   * @param notification Notification payload to send
   * @param scheduleTime Optional date to schedule the task to run at
   * @returns Name of the task
   */
  async enqueueSendNotificationTask(notificationTaskId: string, scheduleTime?: Date) {
    const url = `${this.configService.getOrThrow("BASE_URL")}${
      CloudTaskNameUrlMap[CloudTaskName.SEND_NOTIFICATION]
    }/${notificationTaskId}`;
    if (!scheduleTime) {
      return this.enqueueHttpTaskNow(CloudTaskQueueName.SEND_NOTIFICATION_QUEUE, url);
    }

    const scheduleTimeInSeconds = scheduleTime.getTime() / 1000;

    return this.enqueueHttpTask(CloudTaskQueueName.SEND_NOTIFICATION_QUEUE, url, null, scheduleTimeInSeconds);
  }

  /**
   * Custom methods for each client or use-case can be added below
   */

  /**
   * Enqueues a task to send a notification by batchNumber.
   * @param notificationTaskId The ID of the notification task to enqueue.
   * @returns The name of the task that was enqueued.
   */
  async enqueueSendNotificationTaskBatch(notificationTaskId: string, batchNumber: number): Promise<string> {
    const url = `${this.configService.getOrThrow("BASE_URL")}${
      CloudTaskNameUrlMap[CloudTaskName.SEND_NOTIFICATION]
    }/${notificationTaskId}?batchNumber=${batchNumber}`;

    const now = new Date();
    now.setSeconds(now.getSeconds() + 1); // Add a 1 second buffer to ensure the next batch is scheduled in the future
    const timestamp = now.getTime() / 1000;

    return this.enqueueHttpTask(CloudTaskQueueName.SEND_NOTIFICATION_QUEUE, url, null, timestamp);
  }

  /**
   * Enqueues a task to update a fleet order.
   * @param fleetOrderId The ID of the fleet order to update.
   * @param delayInSeconds Optional amount of time in seconds to delay the task. If not provided, the task is executed immediately.
   * @returns The name of the task.
   */

  async enqueueUpdateFleetTask(fleetOrderId: string, delayInSeconds?: number) {
    return this.enqueueHttpTaskWithDelay(
      CloudTaskQueueName.FLEET_UPDATE_ORDER,
      `${this.configService.getOrThrow("BASE_URL")}${
        CloudTaskNameUrlMap[CloudTaskName.UPDATE_FLEET_ORDER]
      }/${fleetOrderId}`,
      { fleetOrderId },
      delayInSeconds,
    );
  }

  async enqueuePickupOrderNotificationTask(txId: string, scheduleTime?: Date): Promise<string | undefined | null> {
    const url = `${this.configService.getOrThrow("BASE_URL")}${
      CloudTaskNameUrlMap[CloudTaskName.PICKUP_ORDER_REMINDER_NOTIFICATION]
    }?txId=${txId}`;

    if (!scheduleTime) {
      return this.enqueueHttpTaskNow(CloudTaskQueueName.SEND_NOTIFICATION_QUEUE, url);
    }

    const scheduleTimeInSeconds = scheduleTime.getTime() / 1000;

    return this.enqueueHttpTask(CloudTaskQueueName.SEND_NOTIFICATION_QUEUE, url, null, scheduleTimeInSeconds);
  }

  // taskReference is the fully-qualified name of the Cloud Task queue
  removeTaskFromQueue(taskReference: string) {
    return this.client.deleteTask({
      name: taskReference,
    });
  }
}
