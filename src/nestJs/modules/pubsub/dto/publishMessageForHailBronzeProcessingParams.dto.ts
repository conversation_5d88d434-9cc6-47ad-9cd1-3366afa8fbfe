import Joi from "joi";

export interface FareCalculation {
  estimated_fare_fee?: number;
  estimated_tunnel_fee?: number;
  fleet_booking_fee?: number;
  additional_booking_fee?: number;
  dash_booking_fee?: number;
  sub_total?: number;
  dash_transaction_fee?: number;
  total?: number;
  transaction_fees__dash_fee_constant?: number;
  transaction_fees__dash_fee_rate?: number;
}

export interface Itinerary {
  index?: number;
  place_id?: string;
  lat?: number;
  lng?: number;
}

export interface MatchedDriver {
  driver_id?: string;
  license_plate?: string;
  vehicle_class?: string;
  is_dash_meter?: boolean;
  fare_calculation?: FareCalculation;
  name?: string;
  name_tc?: string;
  phone_number?: string;
  vehicle_make?: string;
  vehicle_model?: string;
}

export interface ApplicableDiscounts {
  discount_id_third_party?: string;
  discount_rules_third_party?: string;
  discount_id_dash?: string;
  discount_rules_dash?: string;
}

export interface PublishMessageForHailBronzeProcessingParams {
  id: string;
  user_id?: string;
  user_phone_number?: string;
  language?: string;
  base_fare_estimations_by_area?: Record<string, any>;
  time_of_hail?: Date;
  type?: string;
  status?: string;
  operating_areas?: string[];
  trip_estimation__distance_meters?: number;
  trip_estimation__duration_seconds?: number;
  itinerary?: Itinerary[];
  filter_matrix__is_assistant?: boolean;
  filter_matrix__is_pet_friendly?: boolean;
  matched_driver?: MatchedDriver | null;
  min_max_fare_calculations?: FareCalculation[];
  charges__cancellation_fee?: number;
  prioritize_favorite_drivers?: boolean;
  double_tunnel_fee?: boolean;
  favorite_drivers_online?: string[];
  created_at?: Date;
  updated_at?: Date;
  vehicle_class_fleet_preferences?: Record<string, any>;
  applicable_discounts?: ApplicableDiscounts;
}

export const fareCalculationSchema = Joi.object({
  estimated_fare_fee: Joi.number().optional(),
  estimated_tunnel_fee: Joi.number().optional(),
  fleet_booking_fee: Joi.number().optional(),
  additional_booking_fee: Joi.number().optional(),
  dash_booking_fee: Joi.number().optional(),
  sub_total: Joi.number().optional(),
  dash_transaction_fee: Joi.number().optional(),
  total: Joi.number().optional(),
  transaction_fees__dash_fee_constant: Joi.number().optional(),
  transaction_fees__dash_fee_rate: Joi.number().optional(),
}).optional();

export const itinerarySchema = Joi.object({
  index: Joi.number().optional(),
  place_id: Joi.string().optional(),
  lat: Joi.number().optional(),
  lng: Joi.number().optional(),
}).optional();

export const matchedDriverSchema = Joi.object({
  driver_id: Joi.string().optional(),
  license_plate: Joi.string().optional(),
  vehicle_class: Joi.string().optional(),
  is_dash_meter: Joi.boolean().optional(),
  fare_calculation: fareCalculationSchema,
  name: Joi.string().optional(),
  name_tc: Joi.string().optional(),
  phone_number: Joi.string().optional(),
  vehicle_make: Joi.string().optional(),
  vehicle_model: Joi.string().optional(),
}).optional();

export const applicableDiscountsSchema = Joi.object({
  discount_id_third_party: Joi.string().optional(),
  discount_rules_third_party: Joi.string().optional(),
  discount_id_dash: Joi.string().optional(),
  discount_rules_dash: Joi.string().optional(),
}).optional();

//too many fields and maybe change frequently from hailing-backend, so right now we just use any.
//any just define here, not used actually
export const publishMessageForHailBronzeProcessingParamsSchema = Joi.object({
  id: Joi.string().optional(),
  user_id: Joi.string().optional(),
  user_phone_number: Joi.string().optional(),
  language: Joi.string().optional(),
  base_fare_estimations_by_area: Joi.object().pattern(Joi.string(), Joi.any()).optional(),
  time_of_hail: Joi.date().optional(),
  type: Joi.string().optional(),
  status: Joi.string().optional(),
  operating_areas: Joi.array().items(Joi.string()).optional(),
  trip_estimation__distance_meters: Joi.number().optional(),
  trip_estimation__duration_seconds: Joi.number().optional(),
  itinerary: Joi.array().items(itinerarySchema).optional(),
  filter_matrix__is_assistant: Joi.boolean().optional(),
  filter_matrix__is_pet_friendly: Joi.boolean().optional(),
  matched_driver: matchedDriverSchema.allow(null),
  min_max_fare_calculations: Joi.array().items(fareCalculationSchema).optional(),
  charges__cancellation_fee: Joi.number().optional(),
  prioritize_favorite_drivers: Joi.boolean().optional(),
  double_tunnel_fee: Joi.boolean().optional(),
  favorite_drivers_online: Joi.array().items(Joi.string()).optional(),
  created_at: Joi.date().optional(),
  updated_at: Joi.date().optional(),
  vehicle_class_fleet_preferences: Joi.object().pattern(Joi.string(), Joi.any()).optional(),
  applicable_discounts: applicableDiscountsSchema,
});
