import { Injectable, Inject } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import LoggerServiceAdapter from "../utils/logger/logger.service";
import { UserNotificationFactory } from "../notification/user-notification.factory";
import { NotificationTaskRepository } from "../database/repositories/notificationTask.repository";
import NotificationTask, { NotificationTaskStatusError } from "../database/entities/notificationTask.entity";
import { NotificationSenderRequestPayload, notificationSenderRequestSchema } from "../notification/notification.interface";
import { ValidationService } from "../validation/validation.service";
import { NotificationFactory } from "../notification/notification-method/notification.factory";
import { CloudTaskClientService } from "../cloud-task-client/cloud-task-client.service";
import { errorBuilder } from "../utils/utils/error.utils";

@Injectable()
export class NotificationHandlerV1Service {
  private readonly batchSize: number;
  private readonly maxRetryCount: number;
  constructor(
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
    private readonly userNotificationFactory: UserNotificationFactory,
    private readonly notificationTaskRepository: NotificationTaskRepository,
    private readonly notificationFactory: NotificationFactory,
    private readonly cloudTaskClientService: CloudTaskClientService,
  ) {
    this.batchSize = parseInt(this.configService.get<string>("NOTIFICATIONS_BATCH_SIZE") || "50", 10);
    this.maxRetryCount = parseInt(this.configService.get<string>("NOTIFICATIONS_MAX_RETRY_COUNT") || "3", 10);
  }

  async handleV1Notification(task: NotificationTask, batchNumber: number, retryCount: number): Promise<NotificationTask> {
    const taskId = task.id;
    this.logger.info(`[V1NotificationHandler] Processing task ${taskId} with batch ${batchNumber}`);

    try {
      task.process();
      await this.notificationTaskRepository.save(task);
    } catch (err) {
      if (err instanceof NotificationTaskStatusError) {
        this.logger.warn(
          `[V1NotificationHandler] Task ${taskId} is already in process or completed, skipping.`,
          { task },
        );
        return task;
      }

      throw err;
    }

    try {
      const notificationSender = this.userNotificationFactory.getSender(task.userType);

      const { scheduledAt: _, updatedBy: __, ...taskPayload } = task.payload;

      const [validPayload] = ValidationService.validate<NotificationSenderRequestPayload[]>(
        [
          {
            schema: notificationSenderRequestSchema,
            value: taskPayload,
          },
        ],
        true,
      );

      // Split into smaller batches to reduce memory pressure
      const batchStart = batchNumber * this.batchSize;
      const batchEnd = batchStart + this.batchSize;
      const phoneNumberChunk = validPayload.phoneNumbers.slice(batchStart, batchEnd);

      this.logger.info(
        `[V1NotificationHandler] Processing task ${taskId} with batch number ${batchNumber}`,
        {
          taskId,
          batchStart,
          batchEnd,
          batchNumber,
          phoneNumbersCount: validPayload.phoneNumbers.length,
          batchSize: phoneNumberChunk.length,
        },
      );

      if (phoneNumberChunk.length > 0) {
        // Pass taskId to exclude already processed tokens
        const userTokens = await notificationSender.getValidPushNotificationTokens(taskId, phoneNumberChunk);
        await this.notificationFactory.get(task.type).notifyMany(taskId, userTokens, validPayload.notificationRequest);

        if (validPayload.phoneNumbers.length >= batchEnd) {
          // return to scheduled state
          task.schedule();
          await this.notificationTaskRepository.save(task);
          // If there are more phone numbers, schedule the next batch
          await this.scheduleNextBatchNotificationTask(taskId, batchNumber);

          return task;
        }
      }

      task.complete();
      await this.notificationTaskRepository.save(task);
      this.logger.info(`[V1NotificationHandler] ✅ Successfully completed task ${taskId}`);
      return task;
    } catch (error) {
      this.logger.error(
        "[V1NotificationHandler] Error handling V1 notification task",
        { taskId: task.id },
        error as Error,
      );

      if (retryCount >= this.maxRetryCount) {
        this.logger.error(
          "[V1NotificationHandler] Max retry count reached for the task. Marking as failed.",
          { taskId, retryCount },
        );
        task.fail((error as Error).message);
        await this.notificationTaskRepository.save(task);
        return task;
      }

      // as long as we haven't reached the max retry count, we should tell cloud task to retry
      task.schedule();
      await this.notificationTaskRepository.save(task);
      throw errorBuilder.notification.errorSending();
    }
  }

  private async scheduleNextBatchNotificationTask(notificationTaskId: string, currentBatchNumber: number): Promise<string> {
    return this.cloudTaskClientService.enqueueSendNotificationTaskBatch(notificationTaskId, currentBatchNumber + 1);
  }
}