import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import { BankService } from "../../../nestJs/modules/bank/bank.service";
import { MerchantRepository } from "../../../nestJs/modules/database/repositories/merchant.repository";
import { PaymentInstrumentRepository } from "../../../nestJs/modules/database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../nestJs/modules/database/repositories/paymentTx.repository";
import { PayoutRepository } from "../../../nestJs/modules/database/repositories/payout.repository";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { TxTagRepository } from "../../../nestJs/modules/database/repositories/txTag.repository";
import { UserRepository } from "../../../nestJs/modules/database/repositories/user.repository";
import { DiscountRepository } from "../../../nestJs/modules/database/repositories/discount.repository";
import { TxEventRepository } from "../../../nestJs/modules/database/repositories/txEvent.repository";
import { PaymentService } from "../../../nestJs/modules/payment/payment.service";
import { PubSubService } from "../../../nestJs/modules/pubsub/pubsub.service";
import { StorageService } from "../../../nestJs/modules/storage/storage.service";
import { TransactionService } from "../../../nestJs/modules/transaction/transaction.service";
import { TransactionFactoryService } from "../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";
import { TeamsWebhookService } from "../../../nestJs/modules/teams-webhook/teams-webhook.service";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { DASH_FEE_RECALCULATION } from "../../../nestJs/modules/transaction/dto/txAdjustment.dto";
import decodedIdTokenMock from "../../mockData/firebase/decodedIdToken.mock";
import FakeRepository, {
  mockCreateQueryBuilder,
  mockGetRawOne,
  mockFindOne,
  mockCreateTxAdjustment,
} from "../../utils/services/FakeRepository.specs.utils";
import MockAppDatabaseService from "../../utils/services/FakeAppDatabaseService.specs.utils";
import MockPaymentService from "../../utils/services/FakePaymentService.specs.utils";
import FakeBankService from "../../utils/services/FakeBankService.specs.utils";
import FakeStorageService from "../../utils/services/FakeStorageService.specs.utils";
import FakePubSubService from "../../utils/services/FakePubSubService.specs.utils";
import FakeTransactionFactoryService from "../../utils/services/FakeTransactionFactoryService.specs.utils";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import {
  newTestConfigService,
  newTestEmailService,
  newTestMessageTeamsService,
  newTestSecretService,
  newTestTransactionEventService,
} from "../../utils/services/TestServices.specs.utils";
import { FakeTeamsWebhookService } from "../../utils/services/FakeTeamsWebhookService.specs.utils";

/**
 * Test cases for the adjustTxFromFullRefund method bug
 * 
 * This test suite documents a critical mathematical bug in the full refund calculation.
 * The current implementation uses: -originalAmount + sumAdjustments
 * The correct implementation should use: -(originalAmount + sumAdjustments)
 * 
 * This causes over-refunding when there are negative adjustments (deductions)
 * and under-refunding when there are positive adjustments (additions).
 */
describe("TransactionService - adjustTxFromFullRefund Bug Documentation", () => {
  let service: TransactionService;
  const utilsService = new UtilsService();

  beforeEach(async () => {
    jest.clearAllMocks();

    // Mock the query builder chain for sumAdjustments
    const mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getRawOne: mockGetRawOne,
    };
    mockCreateQueryBuilder.mockReturnValue(mockQueryBuilder);

    // Mock createTxAdjustment to return proper structure
    mockCreateTxAdjustment.mockImplementation(() => ({ adjustmentTx: new Tx(), parentTx: new Tx() }));

    service = new TransactionService(
      new MockAppDatabaseService() as unknown as AppDatabaseService,
      new FakeRepository() as unknown as MerchantRepository,
      new MockPaymentService() as unknown as PaymentService,
      new FakeRepository() as unknown as TxRepository,
      new FakeRepository() as unknown as TxTagRepository,
      new FakeRepository() as unknown as PayoutRepository,
      new FakeBankService() as unknown as BankService,
      new FakeStorageService() as unknown as StorageService,
      new FakePubSubService() as unknown as PubSubService,
      new FakeTransactionFactoryService() as unknown as TransactionFactoryService,
      utilsService,
      FakeLoggerService,
      new FakeRepository() as unknown as PaymentInstrumentRepository,
      new FakeRepository() as unknown as PaymentTxRepository,
      newTestMessageTeamsService(),
      newTestTransactionEventService(),
      new FakeRepository() as unknown as UserRepository,
      newTestSecretService(),
      newTestEmailService(),
      new FakeRepository() as unknown as DiscountRepository,
      new FakeRepository() as unknown as TxEventRepository,
      newTestConfigService(),
      new FakeTeamsWebhookService() as unknown as TeamsWebhookService,
    );
  });

  describe("Mathematical Bug Examples", () => {
    it("Example 1: Original=100, Previous Adjustment=-50 (deduction) → Should refund -50, but current logic would calculate -150", async () => {
      // Setup: Create a mock transaction to find
      const parentTx = new Tx();
      parentTx.id = "parent-tx-1";
      parentTx.payoutAmount = 100;
      parentTx.dashFee = 10;
      parentTx.total = 110;

      // Mock findOne to return our parent transaction
      mockFindOne.mockResolvedValueOnce(parentTx);

      // Mock: Previous adjustments show -50 payout, -5 dash fee, -55 total (deductions)
      mockGetRawOne.mockResolvedValueOnce({
        sumAdjustedPayout: -50,
        sumAdjustedDashFee: -5,
        sumAdjustedTotal: -55,
      });

      console.log("=== Example 1: Original=100, Previous Adjustment=-50 ===");
      console.log("Original amounts:", { payout: 100, dashFee: 10, total: 110 });
      console.log("Previous adjustments:", { payout: -50, dashFee: -5, total: -55 });
      console.log("Current effective (what customer owes):", { payout: 50, dashFee: 5, total: 55 });
      console.log("Full refund should be:", { payout: -50, dashFee: -5, total: -55 });
      console.log("Current buggy implementation would calculate:", { payout: -150, dashFee: -15, total: -165 });
      console.log("Error: Over-refunding by 100 in payout!");

      try {
        const result = await service.dryRunAdjustment(
          parentTx.id,
          { type: DASH_FEE_RECALCULATION.FULL_REFUND, reason: "Full refund test" },
          decodedIdTokenMock,
        );

        console.log("Actual result:", {
          payoutAmount: result.payoutAmount,
          dashFee: result.dashFee,
          total: result.total,
        });

        // The current implementation has additional validation that prevents positive refunds
        // So this test documents what the mathematical result would be
      } catch (error: any) {
        console.log("Current implementation throws error:", error.message);
        // This is expected due to validation logic that prevents positive refund amounts
      }
    });

    it("Example 2: Original=100, Previous Adjustment=+30 (addition) → Should refund -130, but current logic would calculate -70", async () => {
      // Setup: Create a mock transaction to find
      const parentTx = new Tx();
      parentTx.id = "parent-tx-2";
      parentTx.payoutAmount = 100;
      parentTx.dashFee = 10;
      parentTx.total = 110;

      // Mock findOne to return our parent transaction
      mockFindOne.mockResolvedValueOnce(parentTx);

      // Mock: Previous adjustments show +30 payout, +3 dash fee, +33 total (additions)
      mockGetRawOne.mockResolvedValueOnce({
        sumAdjustedPayout: 30,
        sumAdjustedDashFee: 3,
        sumAdjustedTotal: 33,
      });

      console.log("=== Example 2: Original=100, Previous Adjustment=+30 ===");
      console.log("Original amounts:", { payout: 100, dashFee: 10, total: 110 });
      console.log("Previous adjustments:", { payout: 30, dashFee: 3, total: 33 });
      console.log("Current effective (what customer owes):", { payout: 130, dashFee: 13, total: 143 });
      console.log("Full refund should be:", { payout: -130, dashFee: -13, total: -143 });
      console.log("Current buggy implementation would calculate:", { payout: -70, dashFee: -7, total: -77 });
      console.log("Error: Under-refunding by 60 in payout!");

      try {
        const result = await service.dryRunAdjustment(
          parentTx.id,
          { type: DASH_FEE_RECALCULATION.FULL_REFUND, reason: "Full refund test" },
          decodedIdTokenMock,
        );

        console.log("Actual result:", {
          payoutAmount: result.payoutAmount,
          dashFee: result.dashFee,
          total: result.total,
        });
      } catch (error: any) {
        console.log("Current implementation throws error:", error.message);
      }
    });

    it("Example 3: Original=100, No Previous Adjustments → Should refund -100 (this case works correctly)", async () => {
      // Setup: Create a mock transaction to find
      const parentTx = new Tx();
      parentTx.id = "parent-tx-3";
      parentTx.payoutAmount = 100;
      parentTx.dashFee = 10;
      parentTx.total = 110;

      // Mock findOne to return our parent transaction
      mockFindOne.mockResolvedValueOnce(parentTx);

      // Mock: No previous adjustments
      mockGetRawOne.mockResolvedValueOnce({
        sumAdjustedPayout: 0,
        sumAdjustedDashFee: 0,
        sumAdjustedTotal: 0,
      });

      console.log("=== Example 3: Original=100, No Previous Adjustments ===");
      console.log("Original amounts:", { payout: 100, dashFee: 10, total: 110 });
      console.log("Previous adjustments:", { payout: 0, dashFee: 0, total: 0 });
      console.log("Current effective (what customer owes):", { payout: 100, dashFee: 10, total: 110 });
      console.log("Full refund should be:", { payout: -100, dashFee: -10, total: -110 });
      console.log("Current implementation calculates:", { payout: -100, dashFee: -10, total: -110 });
      console.log("This case works correctly!");

      try {
        const result = await service.dryRunAdjustment(
          parentTx.id,
          { type: DASH_FEE_RECALCULATION.FULL_REFUND, reason: "Full refund test" },
          decodedIdTokenMock,
        );

        console.log("Actual result:", {
          payoutAmount: result.payoutAmount,
          dashFee: result.dashFee,
          total: result.total,
        });

        // This should work correctly
        expect(result.payoutAmount).toBe(-100);
        expect(result.dashFee).toBe(-10);
        expect(result.total).toBe(-110);
      } catch (error: any) {
        console.log("Unexpected error:", error.message);
      }
    });
  });

  describe("Fix Verification", () => {
    it("Demonstrates that switching + to - fixes the mathematical bug", () => {
      const testCases = [
        { 
          name: "Deduction case",
          original: 100, 
          adjustment: -50, 
          currentEffective: 50,
          expectedRefund: -50 
        },
        { 
          name: "Addition case",
          original: 100, 
          adjustment: 30, 
          currentEffective: 130,
          expectedRefund: -130 
        },
        { 
          name: "No adjustment case",
          original: 100, 
          adjustment: 0, 
          currentEffective: 100,
          expectedRefund: -100 
        },
        { 
          name: "Large deduction case",
          original: 200, 
          adjustment: -75, 
          currentEffective: 125,
          expectedRefund: -125 
        },
        { 
          name: "Small addition case",
          original: 50, 
          adjustment: 25, 
          currentEffective: 75,
          expectedRefund: -75 
        },
      ];

      console.log("=== Fix Verification ===");
      testCases.forEach(({ name, original, adjustment, currentEffective, expectedRefund }) => {
        // Current buggy implementation: -original + adjustment
        const currentBuggyResult = -original + adjustment;
        
        // Fixed implementation: -original - adjustment (equivalent to -(original + adjustment))
        const fixedResult = -original - adjustment;
        
        // Verify the fix is mathematically correct
        const correctResult = -(original + adjustment);
        
        console.log(`${name}:`);
        console.log(`  Original: ${original}, Adjustment: ${adjustment}`);
        console.log(`  Current effective: ${currentEffective}`);
        console.log(`  Current buggy result: ${currentBuggyResult}`);
        console.log(`  Fixed result: ${fixedResult}`);
        console.log(`  Expected: ${expectedRefund}`);
        console.log(`  Error in current: ${currentBuggyResult - expectedRefund}`);
        console.log("");
        
        expect(fixedResult).toBe(correctResult);
        expect(fixedResult).toBe(expectedRefund);
        expect(original + adjustment + fixedResult).toBe(0); // Net effect should be zero
      });
    });
  });

  describe("Financial Impact Analysis", () => {
    it("Calculates the financial impact of the bug across different scenarios", () => {
      const scenarios = [
        { description: "Small deduction", original: 50, adjustment: -10 },
        { description: "Large deduction", original: 500, adjustment: -100 },
        { description: "Small addition", original: 50, adjustment: 20 },
        { description: "Large addition", original: 500, adjustment: 150 },
        { description: "Multiple small adjustments", original: 100, adjustment: -25 },
        { description: "Multiple large adjustments", original: 1000, adjustment: 300 },
      ];

      console.log("=== Financial Impact Analysis ===");
      scenarios.forEach(({ description, original, adjustment }) => {
        const currentEffective = original + adjustment;
        const correctRefund = -currentEffective;
        const buggyRefund = -original + adjustment;
        const financialError = buggyRefund - correctRefund;
        const errorType = financialError > 0 ? "Under-refunding" : "Over-refunding";
        const errorMagnitude = Math.abs(financialError);

        console.log(`${description}:`);
        console.log(`  Original: $${original}, Adjustment: $${adjustment}`);
        console.log(`  Customer owes: $${currentEffective}`);
        console.log(`  Correct refund: $${correctRefund}`);
        console.log(`  Buggy refund: $${buggyRefund}`);
        console.log(`  ${errorType} by: $${errorMagnitude}`);
        console.log("");

        // Document the error for each scenario
        expect(Math.abs(financialError)).toBeGreaterThan(0);
      });
    });
  });
});
