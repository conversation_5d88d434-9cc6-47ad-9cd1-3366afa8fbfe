import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { DirectionType } from "@nest/modules/validation/dto/listingSchema.dto";

export class OrganizationListingQueryDto {
  @ApiProperty({ description: "Limit", required: false, default: 20 })
  limit?: number;

  @ApiProperty({ description: "Offset", required: false, default: 0 })
  offset?: number;

  @ApiProperty({ description: "Search by organization name", required: false })
  search?: string;

  @ApiProperty({ 
    enum: DirectionType,
    description: "Sort direction", 
    required: false, 
    default: DirectionType.DESC 
  })
  sortDirection?: DirectionType;

  @ApiProperty({ 
    description: "Sort field", 
    required: false, 
    default: "createdAt" 
  })
  sortBy?: string;
}

export const organizationListingQuerySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).optional().default(20),
  offset: Joi.number().integer().min(0).optional().default(0),
  search: Joi.string().optional(),
  sortDirection: Joi.string().valid(...Object.values(DirectionType)).optional().default(DirectionType.DESC),
  sortBy: Joi.string().optional().default("createdAt"),
});
