import { Timestamp } from "firebase-admin/firestore";

export class MeterDocumentWithoutConverter {
  static readonly collectionName = "meters";

  carousel_assets?: Record<string, any>;
  last_garage_update?: Timestamp;
  last_garage_user?: string;
  locked_at?: Timestamp;
  mcu_info?: Record<string, any>;
  settings?: Record<string, any>;
  warranty_expired_at?: Timestamp;

  license_plate: string;
}

export interface MeterRow {
  id: string;
  carousel_assets: string | null;
  last_garage_update: Date | null;
  last_garage_user: string | null;
  locked_at: Date | null;
  mcu_info: string | null;
  settings: string | null;
  warranty_expired_at: Date | null;

  [key: string]: any;
}
