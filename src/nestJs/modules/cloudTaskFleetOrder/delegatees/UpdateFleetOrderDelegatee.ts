import { Injectable } from "@nestjs/common";

import { LogAll } from "@nest/decorators/log-all.decorator";
import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { DriverDocument } from "@nest/modules/appDatabase/documents/driver.document";
import { MeterDocument } from "@nest/modules/appDatabase/documents/meter.document";
import { CloudTaskClientService } from "@nest/modules/cloud-task-client/cloud-task-client.service";
import FleetOrderEntity, { FleetOrderStatus } from "@nest/modules/database/entities/fleetOrder.entity";
import FleetOrderTimelineEntity from "@nest/modules/database/entities/fleetOrderTimeline.entity";
import FleetPartnerEntity from "@nest/modules/database/entities/fleetPartner.entity";
import { PlatformMerchantType } from "@nest/modules/database/entities/merchant.entity";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "@nest/modules/database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "@nest/modules/database/repositories/fleetPartner.repository";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { HailStatus } from "@nest/modules/hailing/dto/updateHail.dto";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { PartnerKey } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";
import { TxEventCreatedBy } from "@nest/modules/me/modules/meTransaction/dto/addEvent.dto";
import { DriverService } from "@nest/modules/merchant/merchantDriver/merchantDriver.service";
import { TxEventType } from "@nest/modules/transaction/dto/txEventType.dto";
import { TransactionEventService } from "@nest/modules/transaction/modules/transactionEvent.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { IUpdateFleetOrderResponse, HailUpdateResponse } from "../interface";
import { UpdateFleetOrderBody } from "../dto/fleetOrder.dto";

import { SyncabUpdateFleetOrderDelegatee } from "./SyncabUpdateFleetOrderDelegatee";

const endStatus = [FleetOrderStatus.COMPLETED, FleetOrderStatus.CANCELLED, FleetOrderStatus.TIMED_OUT];

const partnerKeyToPlatformMerchantType: Record<PartnerKey, PlatformMerchantType> = {
  [PartnerKey.SYNCAB]: PlatformMerchantType.SYNCAB,
};

@LogAll()
@Injectable()
export class UpdateFleetOrderDelegatee {
  constructor(
    private readonly fleetOrderRepository: FleetOrderRepository,
    private readonly syncabUpdateFleetOrderDelegatee: SyncabUpdateFleetOrderDelegatee,
    private readonly fleetOrderTimelineRepository: FleetOrderTimelineRepository,
    private readonly merchantRepository: MerchantRepository,
    private readonly transactionEventService: TransactionEventService,
    private readonly fleetPartnerRepository: FleetPartnerRepository,
    private readonly appDatabaseService: AppDatabaseService,
    private readonly driverService: DriverService,
    private readonly cloudTaskClientService: CloudTaskClientService,
    private readonly hailingApiService: HailingApiService,
  ) {}

  private getHailStatusMap: Record<FleetOrderStatus, HailStatus> = {
    [FleetOrderStatus.MATCHING]: HailStatus.PENDING,
    [FleetOrderStatus.ACCEPT]: HailStatus.ACCEPTED,
    [FleetOrderStatus.APPROACHING]: HailStatus.APPROACHING,
    [FleetOrderStatus.ARRIVED]: HailStatus.ARRIVED,
    [FleetOrderStatus.ON_GOING]: HailStatus.ON_GOING,
    [FleetOrderStatus.COMPLETED]: HailStatus.COMPLETED,
    [FleetOrderStatus.CANCELLED]: HailStatus.CANCELLED,
    [FleetOrderStatus.TIMED_OUT]: HailStatus.TIMED_OUT,
  };

  getDelegatee(partnerKey: PartnerKey) {
    if (partnerKey === PartnerKey.SYNCAB) {
      return this.syncabUpdateFleetOrderDelegatee;
    }
    return null;
  }

  async execute(body: UpdateFleetOrderBody) {
    const fleetOrder = await this.fleetOrderRepository.findOne({
      where: { id: body.fleetOrderId },
    });

    const lastTimeline = await this.fleetOrderTimelineRepository.findLastTimelineByFleetOrderId(body.fleetOrderId);

    if (!fleetOrder || !lastTimeline) {
      throw errorBuilder.fleetTaxi.noFleetOrderFound();
    }

    if (endStatus.includes(fleetOrder.status)) {
      return fleetOrder;
    }

    const delegatee = this.getDelegatee(fleetOrder.partnerKey);

    if (!delegatee) {
      throw errorBuilder.fleetTaxi.noUpdateFleetOrderDelegatee();
    }

    const fleetPartner = await this.fleetPartnerRepository.findOne({
      where: { partnerKey: fleetOrder.partnerKey },
    });

    if (!fleetPartner) {
      throw errorBuilder.fleetTaxi.noFleetPartnerFound();
    }

    if (!fleetPartner.config.METER_ID) {
      throw errorBuilder.fleetTaxi.noMeterFound();
    }

    const fleetMeter = await this.appDatabaseService.meterRepository().findOneById(fleetPartner.config.METER_ID);

    if (!fleetMeter) {
      throw errorBuilder.fleetTaxi.noMeterFound();
    }

    const result = await delegatee.execute(body, fleetOrder);
    const { status, thirdPartyStatus, snapshot } = result;

    if (result.driverPhoneNumber && result.driverLocation.lat && result.driverLocation.lng) {
      await this.hailingApiService.updateDriverHeartBeat(result.driverPhoneNumber, fleetOrder.hailingRequestId, {
        lat: result.driverLocation.lat,
        lng: result.driverLocation.lng,
        heading: null,
        speed: null,
      });
    }

    if (this.shouldCreateNullStatusTimeline(lastTimeline, result)) {
      await this.fleetOrderTimelineRepository.save({
        fleetOrderId: fleetOrder.id,
        thirdPartyStatus,
        snapshot,
      });

      return this.handleNextRun(fleetOrder, fleetPartner, status, false);
    } else if (this.isNoStatusChange(fleetOrder, lastTimeline, result)) {
      return this.handleNextRun(fleetOrder, fleetPartner, status, false);
    }

    if (!status) {
      return this.handleNextRun(fleetOrder, fleetPartner, status, false);
    }

    await this.upsertMerchant(result, fleetOrder.partnerKey);

    const hailUpdateResponse = await this.updateHailingRequestStatus(fleetOrder, result, fleetMeter);

    if (hailUpdateResponse) {
      await this.handleEvent(fleetOrder, result, fleetMeter, hailUpdateResponse);
    }

    if (status === FleetOrderStatus.COMPLETED && hailUpdateResponse) {
      const snapshot = await delegatee.getBookingReceiptSnapshot(fleetOrder);
      if (snapshot) {
        await this.fleetOrderRepository.update(fleetOrder.id, {
          bookingReceiptSnapshot: snapshot as unknown as JSON,
        });
      }

      const trip = await this.appDatabaseService
        .meterTripRepository(fleetMeter.id)
        .findOneById(hailUpdateResponse.tripId);

      if (!trip) {
        throw errorBuilder.meter.tripNotFound(fleetMeter.id, hailUpdateResponse.tripId);
      }

      if (!result.driverPhoneNumber) {
        throw errorBuilder.fleetTaxi.noDriverPhoneNumber();
      }

      await this.driverService.updateMeterTripEndByFleet({
        driverId: result.driverPhoneNumber,
        meterId: fleetMeter.id,
        tripId: hailUpdateResponse.tripId,
        updateTripEndDto: {
          tripEnd: new Date(),
          tripTotal: trip.billing?.estimatedFare || 0,
          fleetPayoutFee: snapshot.bookingFee || trip.billing?.estimatedFare || 0,
        },
      });
    } else if (status === FleetOrderStatus.CANCELLED) {
      const snapshot = await delegatee.getBookingReceiptSnapshot(fleetOrder);
      if (snapshot) {
        await this.fleetOrderRepository.update(fleetOrder.id, {
          bookingReceiptSnapshot: snapshot as unknown as JSON,
        });
      }
      await this.hailingApiService.cancelFleetHailingRequest(fleetOrder.hailingRequestId);
    }

    await this.fleetOrderRepository.update(fleetOrder.id, { status });

    const updatedFleetOrder = await this.fleetOrderRepository.findOne({
      where: { id: fleetOrder.id },
    });

    if (!updatedFleetOrder) {
      throw errorBuilder.fleetTaxi.noFleetOrderFound();
    }

    await this.fleetOrderTimelineRepository.save({
      fleetOrderId: fleetOrder.id,
      status,
      thirdPartyStatus,
      snapshot,
    });

    if (endStatus.includes(status)) {
      return updatedFleetOrder;
    }

    return this.handleNextRun(updatedFleetOrder, fleetPartner, status, true);
  }

  shouldCreateNullStatusTimeline(lastTimeline: FleetOrderTimelineEntity, data: IUpdateFleetOrderResponse) {
    return !data.status && lastTimeline.thirdPartyStatus !== data.thirdPartyStatus;
  }

  isNoStatusChange(
    fleetOrder: FleetOrderEntity,
    lastTimeline: FleetOrderTimelineEntity,
    data: IUpdateFleetOrderResponse,
  ): boolean {
    return fleetOrder.status === data.status && lastTimeline.thirdPartyStatus === data.thirdPartyStatus;
  }

  async handleEvent(
    fleetOrder: FleetOrderEntity,
    data: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
    hailUpdateResponse: HailUpdateResponse,
  ) {
    const handlerMap: Record<
      string,
      (
        fleetOrder: FleetOrderEntity,
        data: IUpdateFleetOrderResponse,
        fleetMeter: MeterDocument,
        hailUpdateResponse: HailUpdateResponse,
      ) => Promise<void>
    > = {
      [FleetOrderStatus.MATCHING]: this.addMatchingEvent.bind(this),
      [FleetOrderStatus.ACCEPT]: this.addAcceptedEvent.bind(this),
      [FleetOrderStatus.APPROACHING]: this.addApproachingEvent.bind(this),
      [FleetOrderStatus.ARRIVED]: this.addArrivedEvent.bind(this),
      [FleetOrderStatus.ON_GOING]: this.addOnGoingEvent.bind(this),
      [FleetOrderStatus.COMPLETED]: this.addCompletedEvent.bind(this),
      [FleetOrderStatus.TIMED_OUT]: this.addTimedOutEvent.bind(this),
      [FleetOrderStatus.CANCELLED]: this.addCancelledEvent.bind(this),
    };

    if (!data.status) {
      return;
    }

    const handler = handlerMap[data.status];

    if (!handler) {
      return;
    }
    await handler(fleetOrder, data, fleetMeter, hailUpdateResponse);
  }

  async addMatchingEvent(fleetOrder: FleetOrderEntity): Promise<void> {
    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
    });
  }

  async addTimedOutEvent(fleetOrder: FleetOrderEntity): Promise<void> {
    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT,
    });
  }

  async addAcceptedEvent(
    fleetOrder: FleetOrderEntity,
    data: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
  ): Promise<void> {
    if (!data.driverPhoneNumber || !data.driverLicensePlate) {
      return;
    }

    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
      content: {
        phoneNumber: data.driverPhoneNumber,
        meter: data.driverLicensePlate,
        fleetMockMeterId: fleetMeter.id,
        licensePlate: data.driverLicensePlate,
        platformMerchantType: partnerKeyToPlatformMerchantType[fleetOrder.partnerKey],
      },
    });
  }

  async addApproachingEvent(
    fleetOrder: FleetOrderEntity,
    data: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
  ): Promise<void> {
    if (!data.driverPhoneNumber || !data.driverLicensePlate) {
      return;
    }

    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION,
    });
  }

  async addCancelledEvent(
    fleetOrder: FleetOrderEntity,
    _data: IUpdateFleetOrderResponse,
    _fleetMeter: MeterDocument,
    _hailUpdateResponse: HailUpdateResponse,
  ): Promise<void> {
    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
    });
  }

  async addOnGoingEvent(
    fleetOrder: FleetOrderEntity,
    data: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
    hailUpdateResponse: HailUpdateResponse,
  ): Promise<void> {
    if (!data.driverPhoneNumber || !data.driverLicensePlate) {
      return;
    }

    await this.fleetOrderRepository.update(fleetOrder.id, { tripTxId: hailUpdateResponse.tripId });

    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
      content: {
        txId: hailUpdateResponse.tripId,
        meterId: data.driverLicensePlate,
        fleetMockMeterId: fleetMeter.id,
      },
    });
  }

  async addCompletedEvent(
    fleetOrder: FleetOrderEntity,
    data: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
    hailUpdateResponse: HailUpdateResponse,
  ): Promise<void> {
    if (!data.driverPhoneNumber || !data.driverLicensePlate) {
      return;
    }

    if (fleetOrder.status !== FleetOrderStatus.ON_GOING) {
      await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
        type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
        content: {
          txId: hailUpdateResponse.tripId,
          meterId: data.driverLicensePlate,
          fleetMockMeterId: fleetMeter.id,
        },
      });
    }
    await this.fleetOrderRepository.update(fleetOrder.id, { tripTxId: hailUpdateResponse.tripId });

    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_ORDER_COMPLETED,
    });
  }

  async addArrivedEvent(fleetOrder: FleetOrderEntity, _data: IUpdateFleetOrderResponse): Promise<void> {
    await this.transactionEventService.addEvent(fleetOrder.txId, TxEventCreatedBy.SYSTEM, {
      type: TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION,
    });
  }

  async updateHailingRequestStatus(
    fleetOrder: FleetOrderEntity,
    result: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
  ): Promise<HailUpdateResponse | null> {
    if (!result.status) {
      return null;
    }
    const hailStatus = this.getHailStatusMap[result.status];

    return this.hailingApiService.updateFleetHailingRequest(fleetOrder, result, fleetMeter, hailStatus);
  }

  async upsertMerchant(data: IUpdateFleetOrderResponse, partnerKey: PartnerKey) {
    if (!data.driverPhoneNumber || !data.driverName || !data.driverLicensePlate) {
      return;
    }

    const driverType = partnerKeyToPlatformMerchantType[partnerKey];

    if (!driverType) {
      throw errorBuilder.fleetTaxi.noDriverTypeForPartnerKey();
    }

    const merchant = await this.merchantRepository.findOne({
      where: { phoneNumber: data.driverPhoneNumber, platformMerchantType: driverType },
    });

    const fireStoreDriverId = `${data.driverPhoneNumber}-${partnerKey}`;

    let driver = await this.appDatabaseService.driverRepository().findOneById(fireStoreDriverId);
    if (!driver) {
      driver = await this.appDatabaseService.driverRepository().createUniqueDocument(fireStoreDriverId, {
        phoneNumber: data.driverPhoneNumber,
        name: data.driverName,
        nameZh: data.driverName,
        id: fireStoreDriverId,
        createdAt: new Date(),
        createdBy: partnerKey,
      } as unknown as DriverDocument);
    }

    if (merchant && driver) {
      return merchant;
    }

    const newMerchant = await this.merchantRepository.save({
      phoneNumber: data.driverPhoneNumber,
      name: data.driverName,
      metadata: {
        driverLicense: data.driverLicensePlate,
        driverName: data.driverName,
      },
      platformMerchantType: driverType,
    });

    return newMerchant;
  }

  async handleNextRun(
    fleetOrder: FleetOrderEntity,
    fleetPartner: FleetPartnerEntity,
    status: IUpdateFleetOrderResponse["status"],
    _isStatusChange: boolean,
  ) {
    await this.cloudTaskClientService.enqueueUpdateFleetTask(
      fleetOrder.id,
      this.fleetPartnerRepository.getUpdateInterval(fleetPartner, status || fleetOrder.status),
    );

    return fleetOrder;
  }
}
