import { Injectable, Inject } from "@nestjs/common";

import LoggerServiceAdapter from "../utils/logger/logger.service";
import NotificationTask from "../database/entities/notificationTask.entity";
import { NotificationFactory } from "../notification/notification-method/notification.factory";
import { TemplateValidationResult, TemplateValidationService } from "../notification/template-validation.service";
import { INotificationRecipientMap } from "../notification/notification-method/notification-method.interface";
import { NotificationRequestDto } from "../notification/dto/notification.dto";

@Injectable()
export class RecipientNotificationProcessorService {
  constructor(
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly notificationFactory: NotificationFactory,
    private readonly templateValidationService: TemplateValidationService,
  ) { }

  /**
   * Process each recipient with personalized template resolution
   */
  async processRecipientsWithTemplates(
    task: NotificationTask,
    validPayload: any,
    userTokenMap: INotificationRecipientMap
  ): Promise<void> {
    const { recipients, notificationRequest } = validPayload;

    if (!recipients || recipients.length === 0) {
      this.logger.warn("[RecipientProcessor] No recipients found, using original notification request");
      await this.notificationFactory.get(task.type).notifyMany(task.id, userTokenMap, notificationRequest);
      return;
    }
    this.logger.info(`[RecipientProcessor] Processing ${recipients.length} recipients with personalized templates`);
    for (const recipient of recipients) {
      await this.processIndividualRecipient(task, recipient, notificationRequest, userTokenMap);
    }
    this.logger.info(`[RecipientProcessor] Completed processing all recipients for task ${task.id}`);
  }

  /**
   * Process a single recipient with personalized templates
   */
  private async processIndividualRecipient(
    task: NotificationTask,
    recipient: Record<string, any>,
    notificationRequest: Record<string, any>,
    allUserTokenMap: INotificationRecipientMap
  ): Promise<void> {
    const recipientKey = this.templateValidationService.extractPhoneFromRecipient(recipient) ?? "";

    if (!recipientKey) {
      this.logger.warn("[RecipientProcessor] No recipient key found for recipient:", recipient);
      return;
    }

    const recipientTokenData = allUserTokenMap[recipientKey];

    if (!recipientTokenData || !recipientTokenData.tokens.length) {
      this.logger.warn(`[RecipientProcessor] No valid tokens found for recipient key: ${recipientKey}`);
      return;
    }

    const personalizedNotificationRequest = this.templateValidationService.resolveNotificationTemplates(
      notificationRequest,
      recipient
    ) as NotificationRequestDto;

    console.log(`🔍 [RecipientProcessor] Personalized notification for ${recipientKey}:`, {
      originalTitle: notificationRequest.titleEn,
      resolvedTitle: personalizedNotificationRequest.titleEn,
      originalBody: notificationRequest.bodyEn,
      resolvedBody: personalizedNotificationRequest.bodyEn,
      recipientData: recipient
    });

    try {
      const singleRecipientTokenMap: INotificationRecipientMap = {
        [recipientTokenData.userID]: recipientTokenData
      };

      await this.notificationFactory.get(task.type).notifyMany(
        task.id,
        singleRecipientTokenMap,
        personalizedNotificationRequest
      );

      this.logger.info(`[RecipientProcessor] ✅ Sent personalized notification to ${recipientKey}`);
    } catch (error: unknown) {
      this.handleNotificationError(error, recipientKey, recipient);
      throw error;
    }
  }

  /**
   * Handle notification errors with proper typing
   */
  private handleNotificationError(error: unknown, recipientPhone: string, recipient: Record<string, any>): void {
    if (error instanceof Error) {
      this.logger.error(
        `[RecipientProcessor] ❌ Failed to send notification to ${recipientPhone}: ${error.message}`,
        {
          errorName: error.name,
          errorMessage: error.message,
          errorStack: error.stack,
          recipientPhone,
          recipientData: recipient
        }
      );
    } else {
      this.logger.error(
        `[RecipientProcessor] ❌ Failed to send notification to ${recipientPhone}: Unknown error`,
        {
          error: String(error),
          recipientPhone,
          recipientData: recipient
        }
      );
    }
  }

  /**
   * Extract all phone numbers from recipients for validation
   */
  extractAllPhoneNumbers(recipients: Array<Record<string, any>>): string[] {
    return this.templateValidationService.extractPhoneNumbers(recipients);
  }

  /**
   * Validate that all recipients have the required template variables
   */
  validateRecipientsForTemplates(
    recipients: Array<Record<string, any>>,
    notificationRequest: Record<string, any>
  ): TemplateValidationResult {
    if (!recipients || recipients.length === 0) {
      throw new Error("No recipients provided for template validation");
    }
    const validationResult = this.templateValidationService.validateTemplatesWithRecipients(
      notificationRequest,
      recipients
    );
    if (!validationResult.isValid) {
      this.logger.error(
        `[RecipientProcessor] ❌ Template validation failed. Missing variables: ${validationResult.missingVariables.join(", ")}. ` +
        `Available columns: ${validationResult.availableColumns.join(", ")}`
      );
    } else {
      this.logger.info(`[RecipientProcessor] ✅ Template validation passed for ${recipients.length} recipients`);
    }
    return validationResult;
  }
}