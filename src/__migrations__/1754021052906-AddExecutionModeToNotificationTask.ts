import { MigrationInterface, QueryRunner } from "typeorm";

export class AddExecutionModeToNotificationTask1754021052906 implements MigrationInterface {
  name = "AddExecutionModeToNotificationTask1754021052906";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "ALTER TABLE \"notification_task\" ADD \"executionMode\" character varying NOT NULL DEFAULT 'v1'"
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "ALTER TABLE \"notification_task\" DROP COLUMN \"executionMode\""
    );
  }
}
