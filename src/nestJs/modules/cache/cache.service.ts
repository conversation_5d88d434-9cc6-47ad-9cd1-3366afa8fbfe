import { Inject, Injectable } from "@nestjs/common";
import { Cacheable } from "cacheable";

import { LogAll } from "@nest/decorators/log-all.decorator";

export enum CacheKeyType {
  PUBLIC_HAILING_REQUEST = "PUBLIC_HAILING_REQUEST:",
}

export const CACHE_TOKEN = "KEYV_CACHE_MANAGER";

@LogAll()
@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_TOKEN) private readonly cacheManager: Cacheable) {}

  async get<T>(key: string): Promise<T | undefined> {
    try {
      return this.cacheManager.get(key);
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return undefined;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<boolean > {
    try {
      return this.cacheManager.set(key, value, ttl);
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
      // Don't throw error to prevent cache failures from breaking the app
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      return this.cacheManager.delete(key);
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  async refresh<T>(key: string, cb: () => Promise<T>, ttl?: number): Promise<T> {
    // Force refresh by deleting first, then setting new value
    await this.del(key);
    const value = await cb();
    await this.set(key, value, ttl);
    return value;
  }

  async reset(key: string): Promise<void> {
    // Ensure key is deleted from all stores
    await this.cacheManager.delete(key);
    // Force a small delay to ensure propagation across stores
    await new Promise((resolve) => setTimeout(resolve, 10));
  }

  // Add method to check cache health
  async healthCheck(): Promise<boolean> {
    try {
      const testKey = "__health_check__";
      const testValue = Date.now().toString();
      await this.set(testKey, testValue, 1000); // 1 second TTL
      const retrieved = await this.get(testKey);
      await this.del(testKey);
      return retrieved === testValue;
    } catch (error) {
      console.error("Cache health check failed:", error);
      return false;
    }
  }

  getCacheConfig(type: CacheKeyType, key: string) {
    const configMap = {
      [CacheKeyType.PUBLIC_HAILING_REQUEST]: {
        ttl: 5 * 1000 * 60,
        key: CacheKeyType.PUBLIC_HAILING_REQUEST + key,
      },
    };
    return configMap[type];
  }
}
