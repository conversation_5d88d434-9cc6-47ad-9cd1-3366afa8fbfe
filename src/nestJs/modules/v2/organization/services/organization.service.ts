import { Injectable, NotFoundException, ForbiddenException } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";

import { OrganizationRepository } from "@nest/modules/database/repositories/organization.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { OrganizationResponseDto } from "../organization.dto";

@Injectable()
export class OrganizationService {
  constructor(
    private readonly organizationRepository: OrganizationRepository,
    private readonly userRepository: UserRepository,
  ) {}

  async getOrganizationById(id: string, user: DecodedIdToken): Promise<OrganizationResponseDto> {
    if (!user || !user.user_id) {
      throw errorBuilder.auth.tokenInvalidRole();
    }

    // First, verify the organization exists
    const organization = await this.organizationRepository.findOne({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Check if the user belongs to this organization
    const userEntity = await this.userRepository.findOne({
      where: {
        appDatabaseId: user.uid,
        organization: { id: organization.id },
      },
      relations: ["organization"],
    });

    if (!userEntity) {
      throw new ForbiddenException("You do not have access to this organization");
    }

    // Return the organization data
    return {
      id: organization.id,
      name: organization.name,
      category: organization.category,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,
    };
  }
}
