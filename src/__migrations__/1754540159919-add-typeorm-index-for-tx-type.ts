import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTypeormIndexForTxType1754540159919 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('CREATE INDEX CONCURRENTLY "idx_tx_type" ON "tx" ("type")');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX "idx_tx_type"');
    }

    public get transaction(): boolean {
        return false;
    }
}
