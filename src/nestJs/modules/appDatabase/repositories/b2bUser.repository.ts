import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { B2bUserDocument } from "../documents/b2bUser.document";

import BaseRepository from "./baseRepository.repository";

class B2bUserRepository extends BaseRepository<B2bUserDocument> {
  constructor(collection: CollectionReference<B2bUserDocument>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default B2bUserRepository;
