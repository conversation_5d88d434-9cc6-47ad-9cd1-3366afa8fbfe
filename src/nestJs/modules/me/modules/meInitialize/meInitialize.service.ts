import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { InsertResult } from "typeorm";

import { PubSubService } from "@nest/modules/pubsub/pubsub.service";

import { UserRepository } from "../../../database/repositories/user.repository";
import { UserService } from "../../../user/user.service";
import { MeNotificationTokenService } from "../meNotificationToken/meNotificationToken.service";
@Injectable()
export class MeInitializeService {
  constructor(
    private readonly meNotificationTokenService: MeNotificationTokenService,
    private readonly userService: UserService,
    @InjectRepository(UserRepository) private userRepository: UserRepository,
    private readonly pubsubService: PubSubService,
  ) { }

  async initialize({ token, deviceId, appDatabaseId, ipAddress }:
    { token?: string, deviceId?: string, appDatabaseId: string, ipAddress?: string }): Promise<InsertResult> {
    const user = await this.userRepository.findAppUserById(appDatabaseId);
    await this.userService.checkAndUpdateKey(appDatabaseId, user);
    await this.saveUserDeviceInfo({ deviceId, userId: user.id, ipAddress });

    // await this.userService.checkAndCopyCampaigns(appDatabaseId); // commented out because it is not used right now, and it will be used in the future and will be changed
    if (token) {
      const insertResult = await this.meNotificationTokenService.upsertNotificationToken(
        { token },
        user,
      );
      return insertResult;
    }
    return { generatedMaps: [], raw: [], identifiers: [] };
  }

  private async saveUserDeviceInfo(
    { deviceId, userId, ipAddress }: { deviceId?: string, userId: string, ipAddress?: string }
  ) {
    if (!deviceId) {
      return;
    }
    return this.pubsubService.publishMessageForUserDeviceInfo({ userId, deviceId, ipAddress, time: new Date() });
  }
}
