import { TxRepository } from "@nest/modules/database/repositories/tx.repository";

export const mockTxRepository = {
  upsertTxAndUpdatePaymentTx: jest.fn(),
  createTxAdjustment: jest.fn(),
  updatePayoutAndGetMerchantsData: jest.fn(),
  getCompletedTxListByUserId: jest.fn(),
  updateHailingTxToCancelled: jest.fn(),
  save: jest.fn(),
  exists: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  findTxByStatusAndUserId: jest.fn(),
  findHistoricHailingTxByStatusAndUserId: jest.fn(),
};

export const MockTxRepositoryProvider = {
  provide: TxRepository,
  useValue: mockTxRepository,
};
