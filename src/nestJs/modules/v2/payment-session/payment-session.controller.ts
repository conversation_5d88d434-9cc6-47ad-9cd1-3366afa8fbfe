import { <PERSON>, <PERSON><PERSON>, <PERSON> } from "@nestjs/common";
import { ApiOkResponse, ApiOperation, ApiTags } from "@nestjs/swagger";

import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";
import { txIdSchema } from "@nest/modules/transaction/dto/tx.dto";
import { apiTags } from "@nest/modules/utils/utils/swagger.utils";
import { UseMasterReadClient } from "@nest/decorators/use-master-read-client";

import { PaymentSessionService } from "./payment-session.service";
import { CreatePaymentSessionResponseDto } from "./dto/create-payment-session.dto";

@Controller("v2/public/payment-session")
@ApiTags(...apiTags.v2_public_hailing)
export class PaymentSessionController {
  constructor(private readonly paymentSessionService: PaymentSessionService) {}

  @Post(":txId")
  @UseMasterReadClient()
  @ApiOperation({ summary: "Create payment session for a given tx id" })
  @ApiOkResponse({ description: "Create payment session for a given tx id", type: CreatePaymentSessionResponseDto })
  createTxSessionForPayment(@Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string) {
    return this.paymentSessionService.createTxSessionForPayment(txId);
  }
}
