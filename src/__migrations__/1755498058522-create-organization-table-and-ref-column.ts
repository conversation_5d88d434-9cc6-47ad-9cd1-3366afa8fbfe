import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateOrganizationTableAndRefColumn1755498058522 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query(
            `CREATE TABLE "organization" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "category" character varying NOT NULL,
                "paymentMethod" character varying NOT NULL DEFAULT 'REAL_TIME',
                "recurringPeriod" character varying NOT NULL DEFAULT 'MONTHLY',
                "createdAt" TIMESTAMP NOT NULL DEFAULT (now() at time zone 'utc'),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT (now() at time zone 'utc'),
                CONSTRAINT "PK_organization" PRIMARY KEY ("id")
            )`
        );

        queryRunner.query(
            "ALTER TABLE \"user\" ADD \"organizationId\" uuid"
        );
        queryRunner.query(
            "ALTER TABLE \"user\" ADD CONSTRAINT \"FK_user_organizationId\" FOREIGN KEY (\"organizationId\") REFERENCES \"organization\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION"
        );

        queryRunner.query(
            "ALTER TABLE \"tx\" ADD \"organizationId\" uuid"
        );
        queryRunner.query(
            "ALTER TABLE \"tx\" ADD CONSTRAINT \"FK_tx_organizationId\" FOREIGN KEY (\"organizationId\") REFERENCES \"organization\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION"
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query('DROP TABLE "organization"');
        queryRunner.query('ALTER TABLE "user" DROP COLUMN "organizationId"');
        queryRunner.query('ALTER TABLE "user" DROP CONSTRAINT "FK_user_organizationId"');
        queryRunner.query('ALTER TABLE "tx" DROP COLUMN "organizationId"');
        queryRunner.query('ALTER TABLE "tx" DROP CONSTRAINT "FK_tx_organizationId"');
    }

}
