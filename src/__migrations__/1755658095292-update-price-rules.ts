import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatePriceRules1755658095292 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // rename fareType to feeType
        await queryRunner.query(
            "ALTER TABLE \"pricing_rule\" RENAME COLUMN \"fareType\" TO \"feeType\""
        );
        // insert a new rule for B2B_APP for DASH_BOOKING_FEE
        await queryRunner.query(
            `INSERT INTO "pricing_rule" ("platformType", "clientType", "orderType", "jsonRule", "startAt", "endAt", "feeType") 
             VALUES ('DASH', 'B2B_APP', 'LIVE', '{ "+": [ 10 ] }', '2025-01-01 00:00:00', '2025-12-31 23:59:59', 'DASH_BOOKING_FEE')`
        );
        await queryRunner.query(
            `INSERT INTO "pricing_rule" ("platformType", "clientType", "orderType", "jsonRule", "startAt", "endAt", "feeType") 
             VALUES ('DASH', 'B2B_APP', 'SCHEDULED', '{ "+": [ 10 ] }', '2025-01-01 00:00:00', '2025-12-31 23:59:59', 'DASH_BOOKING_FEE')`
        );

        // update existing rule for B2B_APP for DASH_TRANSACTION_FEE
        await queryRunner.query(
            "UPDATE \"pricing_rule\" SET \"jsonRule\" = '{ \"*\": [{ \"var\": \"sub_total\" }, 0.1] }' WHERE \"clientType\" = 'B2B_APP' AND \"feeType\" = 'DASH_TRANSACTION_FEE'"
        );
        await queryRunner.query(
            "UPDATE \"pricing_rule\" SET \"jsonRule\" = '{ \"*\": [{ \"var\": \"sub_total\" }, 0.1] }' WHERE \"clientType\" = 'B2B_APP' AND \"feeType\" = 'DASH_TRANSACTION_FEE'"
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // rename feeType back to fareType
        await queryRunner.query(
            "ALTER TABLE \"pricing_rule\" RENAME COLUMN \"feeType\" TO \"fareType\""
        );

        await queryRunner.query(
            "DELETE FROM \"pricing_rule\" WHERE \"clientType\" = 'B2B_APP' AND \"feeType\" = 'DASH_BOOKING_FEE'"
        );
    }

}
