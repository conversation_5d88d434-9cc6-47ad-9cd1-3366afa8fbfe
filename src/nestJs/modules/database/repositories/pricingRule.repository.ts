import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import PricingRuleEntity from "../entities/pricingRule.entity";

@Injectable()
export class PricingRuleRepository extends Repository<PricingRuleEntity> {
  constructor(@InjectRepository(PricingRuleEntity) pricingRuleRepository: Repository<PricingRuleEntity>) {
    super(pricingRuleRepository.target, pricingRuleRepository.manager, pricingRuleRepository.queryRunner);
  }
}
