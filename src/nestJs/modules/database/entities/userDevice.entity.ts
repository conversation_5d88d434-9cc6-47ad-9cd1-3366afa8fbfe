import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Unique } from "typeorm";

import User from "./user.entity";
import { DefaultEntity } from "./defaultEntity";

@Entity("user_device")
@Unique(["userId", "deviceId", "ipAddress"])
export default class UserDeviceEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "uuid" })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: "userId" })
  user: User;

  @Column({ type: "varchar", nullable: true })
  deviceId?: string;

  @Column({ type: "varchar", nullable: true })
  ipAddress?: string;

  @Column({ type: "boolean", nullable: true })
  ipIsProxy?: boolean;

  @Column({ type: "varchar", nullable: true })
  ipResolvedCountry?: string;
}