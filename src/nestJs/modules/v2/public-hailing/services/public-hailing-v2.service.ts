import { Injectable } from "@nestjs/common";

import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { isTxHailing } from "@nest/modules/transaction/dto/tx.dto";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { CacheKeyType, CacheService } from "@nest/modules/cache/cache.service";
import Tx from "@nest/modules/database/entities/tx.entity";

import { PublicDashOrder } from "../dto/public-dash-order.dto";

@Injectable()
export class PublicHailingV2Service {
  constructor(
    private readonly txRepository: TxRepository,
    private readonly hailingApiService: HailingApiService,
    private readonly cacheService: CacheService,
  ) {}

  async getHailingRequest(txId: string) {
    const cacheConfig = this.cacheService.getCacheConfig(CacheKeyType.PUBLIC_HAILING_REQUEST, txId);

    // Use explicit get/set pattern instead of wrap to avoid race conditions
    let tx = await this.cacheService.get<Tx>(cacheConfig.key);
    
    if (!tx) {
      // If not in cache, fetch from database
      const fetchedTx = await this.txRepository.findHailingRequestByTxIdForPublicView(txId);
      
      if (!fetchedTx) {
        throw errorBuilder.transaction.notFound(txId);
      }
      
      tx = fetchedTx;
      
      // Store in cache only after successful fetch
      await this.cacheService.set(cacheConfig.key, tx, cacheConfig.ttl);
    }

    if (!isTxHailing(tx)) {
      throw errorBuilder.transaction.notFound(txId);
    }
    
    if (tx.metadata.status !== TxHailingRequestStatus.PENDING_PAYMENT) {
      const hail = await this.hailingApiService.getHailingRequest(txId);
      return new PublicDashOrder(tx, hail).toJSON();
    }
    return new PublicDashOrder(tx).toJSON();
  }
}
