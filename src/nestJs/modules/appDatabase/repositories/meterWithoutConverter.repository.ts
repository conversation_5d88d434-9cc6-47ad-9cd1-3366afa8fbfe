import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { MeterDocumentWithoutConverter } from "../documents/meterWithoutConverter.document";

import BaseRepository from "./baseRepository.repository";

class MeterRepositoryWithoutConverter extends BaseRepository<MeterDocumentWithoutConverter> {
  constructor(meterCollection: CollectionReference<MeterDocumentWithoutConverter>, logger: LoggerServiceAdapter) {
    super(meterCollection, logger);
  }

  async batchGet(
    query: FirebaseFirestore.Query<MeterDocumentWithoutConverter>,
  ): Promise<MeterDocumentWithoutConverter[]> {
    const snapshot = await query.get();
    return snapshot.docs.map((doc) => {
      const data = doc.data() as MeterDocumentWithoutConverter;
      if (!data.license_plate) {
        const segments = doc.ref.path.split("/");
        const meterId = segments[1];
        return { ...data, license_plate: meterId };
      }
      return data;
    });
  }
}

export default MeterRepositoryWithoutConverter;
