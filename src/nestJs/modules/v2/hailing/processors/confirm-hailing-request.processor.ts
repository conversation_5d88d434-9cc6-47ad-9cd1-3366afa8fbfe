import { Injectable } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";

import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { AppUser } from "@nest/modules/user/dto/user.dto";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import Tx from "@nest/modules/database/entities/tx.entity";
import { TxAppRepository } from "@nest/modules/database/repositories/app.repository";
import { TxAppsNames } from "@nest/modules/apps/dto/Apps.dto";
import { HailingItineraryStepResponse } from "@nest/modules/hailing/dto/hailing.dto";
import { TxHailingMetadata } from "@nest/modules/transaction/dto/txMetadata.dto";
import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";
import { HailingApiCreateOrderResponse } from "@nest/modules/hailing/dto/hailing.api.dto";
import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";
import { PaymentService } from "@nest/modules/payment/payment.service";
import { PaymentGatewayTypes } from "@nest/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { TripEstimation } from "@nest/modules/location/dto/location.dto";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { PaymentInformationStatus } from "@nest/modules/payment/dto/paymentInformationStatus.dto";
import { PubSubService } from "@nest/modules/pubsub/pubsub.service";
import { HailType } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { CacheKeyType, CacheService } from "@nest/modules/cache/cache.service";

import { HailingV2Service } from "../services/hailing-v2.service";
import { ClientType, ConfirmHailingRequestBody, HailingCreateOrderBodyV3 } from "../dto/create-hail.dto";
import { HailingDiscount } from "../dto/discount.dto";
import { PriceRuleService } from "../../priceRule/services/price-rule.service";

@Injectable()
export class ConfirmHailingRequestProcessor {
  constructor(
    private readonly hailingApiService: HailingApiService,
    private readonly userRepository: UserRepository,
    private readonly txRepository: TxRepository,
    private readonly txAppRepository: TxAppRepository,
    private readonly paymentTxRepository: PaymentTxRepository,
    private readonly paymentService: PaymentService,
    private readonly clsService: ClsContextStorageService,
    private readonly hailingV2Service: HailingV2Service,
    private readonly pubsubService: PubSubService,
    private readonly priceRuleService: PriceRuleService,
    private readonly cacheService: CacheService
  ) {}

  async execute(txId: string, body: ConfirmHailingRequestBody, user: DecodedIdToken, token: string): Promise<Tx> {
    try {
      this.clsService.setToken(token);

      const appUser = await this.getAppUser(user);

      const tx = await this.txRepository.findOne({
        where: { id: txId, type: TxTypes.HAILING_REQUEST, user: { id: appUser.id } },
      });

      if (!tx) {
        throw errorBuilder.transaction.notFound(txId);
      }

      const metadata = tx.metadata as TxHailingMetadata;

      const paymentTx = await this.processPayment(tx, body);

      const priceRules = await this.priceRuleService.getActivePricingRules(
        metadata.platformType,
        metadata.clientType as ClientType,
        metadata.type as HailType,
      );

      const hailingResponse = await this.hailingApiService.createHailingOrderV3(
        metadata.request as HailingCreateOrderBodyV3,
        tx,
        metadata.discounts,
        metadata.tripEstimation as TripEstimation,
        metadata.itinerary,
        priceRules,
        undefined,
        paymentTx,
      );

      const updatedTx = await this.afterCreateHailingRequest(
        tx,
        hailingResponse,
        metadata.request as HailingCreateOrderBodyV3,
        metadata.discounts,
        paymentTx,
      );

      await this.pubsubService.publishMessageForHailingTxCreated({ txId: tx.id });

      await this.cacheService.del(this.cacheService.getCacheConfig(CacheKeyType.PUBLIC_HAILING_REQUEST, tx.id).key);
      
      return updatedTx;
    } catch (error) {
      const txId = this.clsService.getCreateHailingRequestTxId();
      if (txId) {
        await this.hailingV2Service.updateTxToFailed(txId);
      }
      throw error;
    }
  }

  async beforeCreateHailingRequest(
    body: HailingCreateOrderBodyV3,
    appUser: AppUser,
    tripItinerary: HailingItineraryStepResponse[],
  ) {
    const txApp = await this.txAppRepository.appByNameOrCreate(TxAppsNames.TAPXI);

    const tx = Tx.fromHailingRequest(body, appUser, txApp, tripItinerary, body.clientType);
    await this.txRepository.save(tx);
    return tx;
  }

  async getAppUser(user: DecodedIdToken) {
    const appUser = await this.userRepository.findAppUserById(user.uid, ["paymentInstruments"]);

    return appUser;
  }

  async afterCreateHailingRequest(
    tx: Tx,
    hailingResponse: HailingApiCreateOrderResponse,
    body: HailingCreateOrderBodyV3,
    discounts: HailingDiscount,
    paymentTx?: PaymentTx,
  ): Promise<Tx> {
    tx.metadata = {
      ...tx.metadata,
      ...hailingResponse,
      charges: {
        cancellationFee: 0,
      },
      discounts,
      request: body,
    } as TxHailingMetadata;

    if (paymentTx) {
      tx.paymentTx = [paymentTx];
    }
    return this.txRepository.save(tx);
  }

  async processPayment(tx: Tx, body: ConfirmHailingRequestBody) {
    const paymentTx = await this.paymentTxRepository.findOne({
      where: { tx: { id: tx.id }, status: PaymentInformationStatus.SUCCESS },
      relations: { tx: true },
      loadEagerRelations: false,
    });

    if (paymentTx) {
      return paymentTx;
    }

    // Convert Python-style dict string to valid JSON by replacing single quotes with double quotes
    const jsonString = body.paymentDetail.response.replace(/'/g, '"');

    const newPaymentTx = this.paymentService.extractPaymentTxInfoFromDocument(
      JSON.parse(jsonString) as any,
      PaymentGatewayTypes.SOEPAY,
    );

    const savedPaymentTx = await this.paymentTxRepository.save({
      ...newPaymentTx,
      tx: { id: tx.id },
    } as PaymentTx);

    if (savedPaymentTx.status !== PaymentInformationStatus.SUCCESS) {
      throw errorBuilder.payment.saleFailed(savedPaymentTx.gatewayResponse);
    }

    return savedPaymentTx;
  }
}
