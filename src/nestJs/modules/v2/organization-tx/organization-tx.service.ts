import { Injectable } from "@nestjs/common";

import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { LogAll } from "@nest/decorators/log-all.decorator";

import { OrganizationTxQueryDto, OrganizationTxResponseDto } from "./dto/organization-tx.dto";

@LogAll()
@Injectable()
export class OrganizationTxService {
  constructor(private readonly txRepository: TxRepository) {}

  async getOrganizationTransactions(
    organizationId: string,
    query: OrganizationTxQueryDto,
  ): Promise<OrganizationTxResponseDto> {
    const { type, page = 1, limit = 50 } = query;
    const skip = (page - 1) * limit;

    // Build the query builder
    const queryBuilder = this.txRepository
      .createQueryBuilder("tx")
      .leftJoinAndSelect("tx.user", "user")
      .leftJoinAndSelect("tx.merchant", "merchant")
      .leftJoinAndSelect("tx.paymentTx", "paymentTx")
      .where("tx.organizationId = :organizationId", { organizationId })
      .orderBy("tx.createdAt", "DESC")
      .skip(skip)
      .take(limit);

    // Add type filter if specified
    if (type) {
      queryBuilder.andWhere("tx.type = :type", { type });
    } else {
      // Default to TRIP and HAILING_REQUEST types only
      queryBuilder.andWhere("tx.type IN (:...types)", { 
        types: [TxTypes.TRIP, TxTypes.HAILING_REQUEST] 
      });
    }

    // Execute the query
    const [items, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      items,
      total,
      page,
      limit,
      totalPages,
    };
  }
}
