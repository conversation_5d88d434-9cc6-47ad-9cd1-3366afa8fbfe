import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { HotelSolutionSoepayResponseDocument } from "../documents/hotelSoepayResponse.document";

import BaseRepository from "./baseRepository.repository";

/**
 * Repository for hotel solution seopay response
 */
class HotelSolutionSoepayResponseRepository extends BaseRepository<HotelSolutionSoepayResponseDocument> {
  constructor(collection: CollectionReference<HotelSolutionSoepayResponseDocument>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default HotelSolutionSoepayResponseRepository;
