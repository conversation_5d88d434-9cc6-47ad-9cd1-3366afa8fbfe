import { MigrationInterface, QueryRunner } from "typeorm";

export class AddVpnCountryToUserDeviceTable1755840757984 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "user_device"
      ADD COLUMN "ipIsProxy" BOOLEAN,
      ADD COLUMN "ipResolvedCountry" VARCHAR;
    `);

    await queryRunner.query(`
      ALTER TABLE "user_device"
      ADD CONSTRAINT user_device_userid_deviceid_ipaddress_unique
      UNIQUE ("userId", "deviceId", "ipAddress");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "user_device"
      DROP CONSTRAINT user_device_userid_deviceid_ipaddress_unique;
    `);

    await queryRunner.query(`
      ALTER TABLE "user_device"
      DROP COLUMN "ipIsProxy",
      DROP COLUMN "ipResolvedCountry";
    `);
  }
}