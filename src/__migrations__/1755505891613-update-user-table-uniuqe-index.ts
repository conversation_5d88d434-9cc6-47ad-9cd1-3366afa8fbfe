import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateUserTableUniuqeIndex1755505891613 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX "public"."registered_user_phoneNumber_appDatabaseId_deletedAt"');
        await queryRunner.query(
            'CREATE UNIQUE INDEX "registered_user_phoneNumber_appDatabaseId_deletedAt_organizationId" ON "user" ("phoneNumber", "organizationId") WHERE "deletedAt" IS NULL AND "appDatabaseId" IS NOT NULL',
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX "public"."registered_user_phoneNumber_appDatabaseId_deletedAt_organizationId"');
        await queryRunner.query(
            'CREATE UNIQUE INDEX "registered_user_phoneNumber_appDatabaseId_deletedAt" ON "user" ("phoneNumber") WHERE "deletedAt" IS NULL AND "appDatabaseId" IS NOT NULL',
        );
    }

}
