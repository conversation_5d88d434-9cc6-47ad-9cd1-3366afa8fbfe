import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import dayjs from "@nest/modules/utils/dayjs";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { CloudTaskClientService } from "../cloud-task-client/cloud-task-client.service";
import { TxRepository } from "../database/repositories/tx.repository";
import { TeamsWebhookService } from "../teams-webhook/teams-webhook.service";
import { TxHailingMetadata } from "../transaction/dto/txMetadata.dto";
import { HailType } from "../transaction/dto/txHailingRequest.dto";

@Injectable()
export class TeamOrderNotificationService {
  constructor(
    private teamWebhookService: TeamsWebhookService,
    private txRepository: TxRepository,
    private configService: ConfigService,
    private cloudTaskClientService: CloudTaskClientService,
    private appDatabaseService: AppDatabaseService,
  ) {}

  async schedulePickupReminderNotification(txId: string) {
    const notificationConfig = await this.appDatabaseService.configurationRepository().getNotificationConfig();

    const tx = await this.txRepository.findOne({ where: { id: txId }, relations: ["user", "merchant"] });

    if (!tx) {
      return this.teamWebhookService.sendErrorMessage(`Order not found for id ${txId}`);
    }

    if (!this.isHailingMetadata(tx.metadata)) {
      return this.teamWebhookService.sendErrorMessage(`Invalid metadata type for hailing order ${txId}`);
    }

    if (tx.metadata.type === HailType.LIVE) {
      return tx;
    }

    const metaData = tx.metadata;

    const firstReminderTime = dayjs(metaData.time).add(
      notificationConfig.beforeMinutesThresholdBeforeFirstOrderPickupNotification,
      "minutes",
    );

    const secondReminderTime = dayjs(metaData.time).add(
      notificationConfig.beforeMinutesThresholdBeforeSecondOrderPickupNotification,
      "minutes",
    );

    if (dayjs().isBefore(firstReminderTime)) {
      await this.cloudTaskClientService.enqueuePickupOrderNotificationTask(txId, firstReminderTime.toDate());
    }

    if (dayjs().isBefore(secondReminderTime)) {
      await this.cloudTaskClientService.enqueuePickupOrderNotificationTask(txId, secondReminderTime.toDate());
    }

    return tx;
  }

  isHailingMetadata = (metadata: unknown): metadata is TxHailingMetadata => {
    return metadata !== null && typeof metadata === "object" && "platformType" in metadata;
  };

  async sendPickupReminderNotification(txId: string) {
    const tx = await this.txRepository.findOne({ where: { id: txId }, relations: ["user", "merchant"] });
    if (!tx) {
      return this.teamWebhookService.sendErrorMessage(`Order not found for id ${txId}`);
    }

    if (!this.isHailingMetadata(tx.metadata)) {
      return this.teamWebhookService.sendErrorMessage(`Invalid metadata type for hailing order ${txId}`);
    }

    const metaData = tx.metadata;

    if (metaData.status === "CANCELLED") {
      return tx;
    }

    const minutesLeft = dayjs().diff(dayjs(metaData.time), "minutes");

    await this.teamWebhookService.sendSuccessMessage(
      `
          TxId: ${tx.id}
          User Phone Number: ${tx.user?.phoneNumber}
          Driver Phone Number: ${tx.merchant?.phoneNumber || "N/A"}
          Driver License Plate: ${metaData.licensePlate || "N/A"}
          Driver Name: ${tx.merchant?.name || "N/A"}
          Order Time: ${dayjs(tx.createdAt).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss")}
          Schedule Time: ${dayjs(metaData?.time).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss")}
        `,
      `Reminder for Scheduled Order for contact driver ${minutesLeft} minutes left before scheduled pickup time`,
      [{ name: "Admin URL", value: `${this.configService.getOrThrow("WEB_ADMIN_URL")}/transactions/${tx.id}` }],
    );

    return tx;
  }

  async sendOrderCreatedNotification(txId: string) {
    const tx = await this.txRepository.findOne({ where: { id: txId }, relations: ["user", "merchant"] });
    if (!tx) {
      return this.teamWebhookService.sendErrorMessage(`Order not found for id ${txId}`);
    }

    // Type guard to check if metadata is TxHailingMetadata

    if (!this.isHailingMetadata(tx.metadata)) {
      return this.teamWebhookService.sendErrorMessage(`Invalid metadata type for hailing order ${txId}`);
    }

    const metaData = tx.metadata;

    if (metaData.type === HailType.LIVE) {
      return tx;
    }

    return this.teamWebhookService.sendSuccessMessage(
      `
      TxId: ${tx.id}
      User Phone Number: ${tx.user?.phoneNumber}
      Order Time: ${dayjs(tx.createdAt).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss")}
      Schedule Time: ${dayjs(metaData?.time).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss")}
    `,
      "Order Created",
      [{ name: "Admin URL", value: `${this.configService.getOrThrow("WEB_ADMIN_URL")}/transactions/${tx.id}` }],
    );
  }

  async sendOrderStatusChangedNotification(txId: string, status: string) {
    const tx = await this.txRepository.findOne({ where: { id: txId }, relations: ["user", "merchant"] });
    if (!tx) {
      return this.teamWebhookService.sendErrorMessage(`Order not found for id ${txId}`);
    }

    if (!this.isHailingMetadata(tx.metadata)) {
      return this.teamWebhookService.sendErrorMessage(`Invalid metadata type for hailing order ${txId}`);
    }

    const metaData = tx.metadata;

    if (metaData.type === HailType.LIVE) {
      return tx;
    }

    if (metaData.type !== "SCHEDULED" || !metaData.request.time) {
      return tx;
    }

    return this.teamWebhookService.sendSuccessMessage(
      `
      TxId: ${tx.id}
      User Phone Number: ${tx.user?.phoneNumber}
      Driver Phone Number: ${tx.merchant?.phoneNumber || "N/A"}
      Driver License Plate: ${metaData.licensePlate || "N/A"}
      Driver Name: ${tx.merchant?.name || "N/A"}
      Order Time: ${dayjs(tx.createdAt).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss")}
      Schedule Time: ${dayjs(metaData?.time).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss")}
    `,
      `Order status changed to ${status} for order ${txId}`,
      [{ name: "Admin URL", value: `${this.configService.getOrThrow("WEB_ADMIN_URL")}/transactions/${tx.id}` }],
    );
  }
}
