import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { PaymentService } from "@nest/modules/payment/payment.service";

export const mockPaymentService = {
  processCapture: jest.fn(),
  processSale: jest.fn(),
  processAuth: jest.fn(),
  extractPaymentTxInfoFromDocument: jest.fn(),
  findLastSuccessAuthWithoutCaptureOrVoid: jest.fn(),
  findLastSuccessSaleWithoutVoid: jest.fn(),
  findAllPreviousAuthAndSaleWithoutCaptureAndVoid: jest.fn(),
  findAllSalesInProcessingStatus: jest.fn(),
  processVoid: jest.fn(),
  processRefund: jest.fn(),
  isStillProcessing: jest.fn(),
  voidPayment: jest.fn(),
  refundPayment: jest.fn(),
  capturePayment: jest.fn(),
  processAuthsToVoid: jest.fn(),
  enquirePaymentTx: jest.fn(),
  searchPaymentTx: jest.fn(),
  getPaymentTxById: jest.fn(),
  voidPaymentWithTxId: jest.fn(),
  processVoidJobs: jest.fn(),
};

export const MockPaymentServiceProvider = {
  provide: PaymentService,
  useValue: mockPaymentService,
};


export const MockPaymentTxRepository = {
  find: jest.fn(),
  findOne: jest.fn(),
  save: jest.fn(),
  upsertPaymentTx: jest.fn(),
};

export const MockPaymentTxRepositoryProvider = {
  provide: PaymentTxRepository,
  useValue: MockPaymentTxRepository,
};
