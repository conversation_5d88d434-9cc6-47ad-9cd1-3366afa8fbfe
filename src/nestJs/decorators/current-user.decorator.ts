import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { auth } from "firebase-admin";

export type FirebaseUser = auth.DecodedIdToken;

export const CurrentUser = createParamDecorator(
    (data: keyof FirebaseUser | undefined, context: ExecutionContext) => {
        const request = context.switchToHttp().getRequest();
        const user: FirebaseUser = request.user;

        return data ? user?.[data] : user;
    },
);