import { Injectable } from "@nestjs/common";
import { LessThanOrEqual, MoreThanOrEqual } from "typeorm";

import { PricingRuleRepository } from "@nest/modules/database/repositories/pricingRule.repository";
import PricingRuleEntity from "@nest/modules/database/entities/pricingRule.entity";
import { HailType } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { PlatformType, ClientType } from "../../hailing/dto/create-hail.dto";

@Injectable()
export class PriceRuleService {
  constructor(private readonly pricingRuleRepository: PricingRuleRepository) {}

  async getActivePricingRules(
    platformType: PlatformType,
    clientType: ClientType,
    orderType: HailType,
  ): Promise<PricingRuleEntity[]> {
    const now = new Date();
    const pricingRule = await this.pricingRuleRepository.find({
      where: {
        platformType,
        clientType,
        orderType,
        startAt: LessThanOrEqual(now),
        endAt: MoreThanOrEqual(now),
      },
    });

    if (pricingRule.length === 0) {
      throw errorBuilder.priceRule.notFound(platformType, clientType, orderType);
    }

    return pricingRule;
  }
}
