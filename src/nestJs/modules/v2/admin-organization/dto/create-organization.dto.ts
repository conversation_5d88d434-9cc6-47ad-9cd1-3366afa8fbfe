import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { ServiceCategory } from "@nest/modules/database/entities/organizationService.entity";

export class CreateOrganizationDto {
  @ApiProperty({ description: "Organization name" })
  name: string;

  @ApiProperty({ 
    enum: ServiceCategory, 
    description: "Organization category" 
  })
  category: ServiceCategory;
}

export const createOrganizationSchema = Joi.object({
  name: Joi.string().min(1).max(255).required().description("Organization name"),
  category: Joi.string().valid(...Object.values(ServiceCategory)).required().description("Organization category"),
});
