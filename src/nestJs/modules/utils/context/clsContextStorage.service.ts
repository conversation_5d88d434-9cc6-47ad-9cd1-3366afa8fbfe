import { randomUUID } from "crypto";

import { Injectable } from "@nestjs/common";
import { CLS_ID, ClsService } from "nestjs-cls";
import { EntityManager } from "typeorm";


export enum ClsContextStorageKeys {
  APP_USER_ID = "appUserId",
  CREATE_HAILING_REQUEST_TX_ID = "createHailingRequestTx",
  TOKEN = "token",
  MASTER_ENTITY_MANAGER = "masterEntityManager",
}

@Injectable()
export default class ClsContextStorageService {
  constructor(private readonly cls: ClsService) {}

  public get<T>(key: string): T | undefined {
    return this.cls.get(key);
  }

  public setContextId(id: string) {
    this.cls.set(CLS_ID, id);
  }

  public setAppUserId(userId: string) {
    this.cls.set(ClsContextStorageKeys.APP_USER_ID, userId);
  }

  public getAppUserId(): string | undefined {
    return this.cls.get(ClsContextStorageKeys.APP_USER_ID);
  }

  public setCreateHailingRequestTxId(txId: string) {
    this.cls.set(ClsContextStorageKeys.CREATE_HAILING_REQUEST_TX_ID, txId);
  }

  public setToken(token: string) {
    this.cls.set(ClsContextStorageKeys.TOKEN, token);
  }

  public getToken() {
    return this.cls.get(ClsContextStorageKeys.TOKEN);
  }

  setMasterEntityManager(entityManager: EntityManager) {
    this.cls.set(ClsContextStorageKeys.MASTER_ENTITY_MANAGER, entityManager);
  }

  getMasterEntityManager(): EntityManager | undefined {
    return this.cls.get(ClsContextStorageKeys.MASTER_ENTITY_MANAGER);
  }

  public getContextId(): string {
    if (!this.cls.getId()) {
      const id = randomUUID();
      try {
        this.cls.set(CLS_ID, id);
        return id;
      } catch (error) {
        return id;
      }
    }
    return this.cls.getId();
  }

  public getContext() {
    return {
      clsContext: {
        correlationId: this.getContextId(),
        appUserId: this.getAppUserId(),
      },
    };
  }

  public getCreateHailingRequestTxId(): string | undefined {
    return this.cls.get(ClsContextStorageKeys.CREATE_HAILING_REQUEST_TX_ID);
  }

  public set<T>(key: string, value: T): void {
    this.cls.set(key, value);
  }
}
