import { <PERSON>, Get, Param } from "@nestjs/common";
import { ApiOperation, ApiTags, ApiOkResponse } from "@nestjs/swagger";

import { apiTags } from "@nest/modules/utils/utils/swagger.utils";
import { txIdSchema } from "@nest/modules/transaction/dto/tx.dto";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";
import { UseMasterReadClient } from "@nest/decorators/use-master-read-client";

import { PublicHailingV2Service } from "./services/public-hailing-v2.service";
import { PublicDashOrder } from "./dto/public-dash-order.dto";

@Controller("v2/public/hailing")
@ApiTags(...apiTags.v2_public_hailing)
export class PublicHailingV2Controller {
  constructor(private readonly publicHailingV2Service: PublicHailingV2Service) {}

  @Get(":txId")
  @UseMasterReadClient()
  @ApiOperation({ summary: "Get hailing request by tx id for public view" })
  @ApiOkResponse({ description: "Get hailing request by tx id for public view", type: PublicDashOrder })
  getHailingRequest(@Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string) {
    return this.publicHailingV2Service.getHailingRequest(txId);
  }
}
