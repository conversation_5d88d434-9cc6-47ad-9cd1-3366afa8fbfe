import { Injectable } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";
import dayjs from "dayjs";

import { CampaignService } from "@nest/modules/campaign/campaign.service";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { PaymentChannelType } from "@nest/modules/campaign/dto/campaign.dto";
import { RuleParams } from "@nest/modules/jsonLogic/dto/ruleParams.dto";
import { TxTypes, SubTxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { LogAll } from "@nest/decorators/log-all.decorator";
import { HailType } from "@nest/modules/transaction/dto/txHailingRequest.dto";

import { CreateHailQuoteResponse, HailingGetQuoteBodyV3 } from "../dto/create-hail.dto";
import { HailingV2Service } from "../services/hailing-v2.service";
import { PriceRuleService } from "../../priceRule/services/price-rule.service";

@LogAll()
@Injectable()
export class CreateHailingQuoteProcessor {
  constructor(
    private campaignService: CampaignService,
    private hailingApiService: HailingApiService,
    private clsService: ClsContextStorageService,
    private userRepository: UserRepository,
    private hailingV2Service: HailingV2Service,
    private priceRuleService: PriceRuleService,
  ) {}

  async execute(body: HailingGetQuoteBodyV3, user: DecodedIdToken, token: string): Promise<CreateHailQuoteResponse> {
    this.clsService.setToken(token);

    const appUser = await this.userRepository.findAppUserById(user.uid, ["paymentInstruments"]);

    if (!appUser) {
      throw errorBuilder.user.missing();
    }

    const validPaymentInstrument = await this.hailingV2Service.getValidPaymentInstrument(
      appUser,
      body.paymentInstrumentId,
    );

    const ruleParams = new RuleParams({
      userId: appUser.id,
      transactionType: TxTypes.TRIP,
      transactionSubtype: SubTxTypes.HAILING,
      paymentInstrumentType: validPaymentInstrument?.cardType,
      paymentChannel: PaymentChannelType.APP,
      originPlaceId: body.placeIds[0],
      destinationPlaceId: body.placeIds[body.placeIds.length - 1],
      timeOfDay: dayjs(body.time).format("HH:mm"),
      dayOfWeek: dayjs(body.time).day() === 0 ? 7 : dayjs(body.time).day(),
    });

    const [thirdPartyCampaign, dashCampaign] = await this.campaignService.getApplicableCampaigns(ruleParams);

    const discount = {
      discountIdThirdParty: thirdPartyCampaign?.id,
      discountRulesThirdParty: thirdPartyCampaign?.discountRules,
      discountIdDash: dashCampaign?.id,
      discountRulesDash: dashCampaign?.discountRules,
    };

    const hailType = body.time ? HailType.SCHEDULED : HailType.LIVE;

    const priceRules = await this.priceRuleService.getActivePricingRules(
      body.platformType,
      body.clientType,
      hailType
    );

    return this.hailingApiService.getQuotes(body, discount, priceRules);
  }
}
