import { Body, Controller, Get, Param, Post, Req } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiBadRequestResponse, ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Request } from "express";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "@nest/modules/utils/utils/swagger.utils";

import { HailingOrder } from "../meTx/dto/me-tx.dto";

import {
  HailingCreateOrderBodyV3,
  HailingGetQuoteBodyV3,
  CreateHailQuoteResponse,
  ConfirmHailingRequestBody,
} from "./dto/create-hail.dto";
import { CreateHailingQuoteProcessor } from "./processors/create-hailing-quote.processor";
import { CreateHailingRequestProcessor } from "./processors/create-hailing-request.processor";
import { ConfirmHailingRequestProcessor } from "./processors/confirm-hailing-request.processor";
import { CancelHailingRequestProcessor } from "./processors/cancel-hailing-request.processor";
import { HailingV2Service } from "./services/hailing-v2.service";
import { HailingCancellationFeeResponse } from "./dto/cancel-hail.dto";

@Controller("v2/me/hailing")
@ApiBearerAuth()
@ApiTags(...apiTags.v2_me_hailing)
export class HailingV2Controller {
  constructor(
    private readonly createHailingRequestProcessor: CreateHailingRequestProcessor,
    private readonly createHailingQuoteProcessor: CreateHailingQuoteProcessor,
    private readonly confirmHailingRequestProcessor: ConfirmHailingRequestProcessor,
    private readonly cancelHailingRequestProcessor: CancelHailingRequestProcessor,
    private readonly hailingV2Service: HailingV2Service,
  ) {}

  @Post("/")
  @ApiOperation({
    summary: "Creates an order",
  })
  @ApiResponse({ status: 201, description: "Creates an order" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.reverseGeocode.invalidParams(),
      errorBuilder.location.reverseGeocode.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async createHailingRequest(@Body() body: HailingCreateOrderBodyV3, @Req() req: Request): Promise<HailingOrder> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    const tx = await this.createHailingRequestProcessor.execute(body, user, req.headers.authorization);

    return new HailingOrder(tx).toJSON();
  }

  @Post("quotes")
  @ApiOperation({
    summary: "Get quotes",
  })
  @ApiResponse({ status: 200, description: "Get quotes", type: CreateHailQuoteResponse })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async getQuotes(@Body() body: HailingGetQuoteBodyV3, @Req() req: Request) {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    return this.createHailingQuoteProcessor.execute(body, user, req.headers.authorization);
  }

  @Post(":txId/confirm")
  @ApiOperation({
    summary: "Confirm order",
  })
  @ApiResponse({ status: 201, description: "Confirm order" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.reverseGeocode.invalidParams(),
      errorBuilder.location.reverseGeocode.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async confirmHalingRequest(
    @Param("txId") txId: string,
    @Body() body: ConfirmHailingRequestBody,
    @Req() req: Request,
  ): Promise<HailingOrder> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    const tx = await this.confirmHailingRequestProcessor.execute(txId, body, user, req.headers.authorization);

    return new HailingOrder(tx).toJSON();
  }

  @Post(":txId/cancel")
  @ApiOperation({
    summary: "Cancel order",
  })
  @ApiResponse({ status: 201, description: "Cancel order" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async cancelHalingRequest(@Param("txId") txId: string, @Req() req: Request): Promise<HailingOrder | null> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    return this.cancelHailingRequestProcessor.execute(txId, user, req.headers.authorization);
  }

  @Get(":txId/cancellation_fee")
  @ApiOperation({
    summary: "Get cancellation fee",
  })
  @ApiResponse({ status: 200, description: "Get cancellation fee", type: HailingCancellationFeeResponse })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async getCancellationFee(@Param("txId") txId: string): Promise<HailingCancellationFeeResponse> {
    return this.hailingV2Service.getCancellationFee(txId);
  }
}
