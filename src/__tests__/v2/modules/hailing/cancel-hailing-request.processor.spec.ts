import { Test, TestingModule } from "@nestjs/testing";

import { MockCreateFleetOrderDelegatee } from "@tests/v2/mockModules/mockMeTaxiModule";
import { MockClsContextStorageServiceProvider } from "@tests/v2/mockModules/mockClsModule";
import { MockHailingApiServiceProvider } from "@tests/v2/mockModules/mockHailingApiModule";
import { MockPubsubServiceProvider } from "@tests/v2/mockModules/mockPubsubModule";
import { MockUserRepository } from "@tests/v2/mockModules/mockUserModule";
import { MockCampaignServiceProvider } from "@tests/v2/mockModules/mockCampaignModule";
import { MockAppDatabaseServiceProvider } from "@tests/v2/mockModules/mockAppDatabaseModule";
import { MockHailingV2ServiceProvider } from "@tests/v2/mockModules/mockHailingV2Module";
import { MockTxRepositoryProvider } from "@tests/v2/mockModules/nockTxModule";
import { MockTxAppRepositoryProvider } from "@tests/v2/mockModules/mockTxAppModule";
import { MockLocationServiceProvider } from "@tests/v2/mockModules/mockLocationModule";
import { UtilsModule } from "@nest/modules/utils/utils.module";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { MockLoggerServiceAdapterProvider } from "@tests/v2/mockModules/mockLoggerServiceAdapter";
import { mockTxRepositoryFactory } from "@tests/v2/mockFactories/mockTxRepositoryFactory";
import { mockUserRepositoryFactory } from "@tests/v2/mockFactories/mockUserRepositoryFactory";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { MockTransactionEventServiceProvider } from "@tests/v2/mockModules/mockTranscationEventModule";
import { TransactionEventService } from "@nest/modules/transaction/modules/transactionEvent.service";
import { CancelHailingRequestProcessor } from "@nest/modules/v2/hailing/processors/cancel-hailing-request.processor";
import { HailType, TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import dayjs from "dayjs";

describe("CancelHailingRequestProcessor", () => {
  let processor: CancelHailingRequestProcessor;
  let txRepository: TxRepository;
  let hailingApiService: HailingApiService;
  let userRepository: UserRepository;
  let transactionEventService: TransactionEventService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [UtilsModule.forRoot()],
      providers: [
        MockLoggerServiceAdapterProvider,
        CancelHailingRequestProcessor,
        MockClsContextStorageServiceProvider,
        MockCreateFleetOrderDelegatee,
        MockHailingApiServiceProvider,
        MockPubsubServiceProvider,
        MockUserRepository,
        MockCampaignServiceProvider,
        MockHailingV2ServiceProvider,
        MockAppDatabaseServiceProvider,
        MockTxRepositoryProvider,
        MockTxAppRepositoryProvider,
        MockLocationServiceProvider,
        MockTransactionEventServiceProvider,
      ],
      controllers: [],
      exports: [],
    }).compile();

    processor = moduleRef.get(CancelHailingRequestProcessor);
    txRepository = moduleRef.get(TxRepository);
    hailingApiService = moduleRef.get(HailingApiService);
    userRepository = moduleRef.get(UserRepository);
    transactionEventService = moduleRef.get(TransactionEventService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const txId = "6cf2d323-8138-4a38-8230-c43a1925d40f";

  describe(".execute", () => {
    describe("when status is PENDING_PAYMENT", () => {
      it("should just cancel a hailing request no need to call hailingApi to cancel", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository, {
          metadata: { status: TxHailingRequestStatus.PENDING_PAYMENT },
        });

        await expect(processor.execute(txId, {} as any, "123")).resolves.not.toThrow();
        expect(hailingApiService.cancelHailingRequest).not.toHaveBeenCalled();
        expect(transactionEventService.addEvent).toHaveBeenCalled();
      });
    });

    describe("when status is PENDING", () => {
      it("when request time is in the future should call hailingApi to cancel", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository, {
          metadata: { status: TxHailingRequestStatus.PENDING, time: dayjs().add(1, "hour").toISOString() },
        });

        await expect(processor.execute(txId, {} as any, "123")).resolves.not.toThrow();
        expect(hailingApiService.cancelHailingRequest).toHaveBeenCalled();
        expect(transactionEventService.addEvent).not.toHaveBeenCalled();
      });

      it("when request time is in the future should just cancel a hailing request no need to call hailingApi to cancel", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository, {
          metadata: { status: TxHailingRequestStatus.PENDING, time: dayjs().add(-1, "hour").toISOString(), type: HailType.SCHEDULED },
        });

        await expect(processor.execute(txId, {} as any, "123")).resolves.not.toThrow();
        expect(hailingApiService.cancelHailingRequest).not.toHaveBeenCalled();
        expect(transactionEventService.addEvent).toHaveBeenCalled();
      });
    });

    describe("when status is CANCELLED", () => {
      it("should just return the tx no need to call hailingApi to cancel", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository, {
          metadata: { status: TxHailingRequestStatus.CANCELLED },
        });

        await expect(processor.execute(txId, {} as any, "123")).resolves.not.toThrow();
        expect(hailingApiService.cancelHailingRequest).not.toHaveBeenCalled();
        expect(transactionEventService.addEvent).not.toHaveBeenCalled();
      });
    });

    describe("when status is COMPLETED", () => {
      it("should just return the tx no need to call hailingApi to cancel", async () => {
        mockUserRepositoryFactory.mockUserFindAppUserById(userRepository);
        mockTxRepositoryFactory.mockHailingTxFindOne(txRepository, {
          metadata: { status: TxHailingRequestStatus.COMPLETED },
        });

        await expect(processor.execute(txId, {} as any, "123")).resolves.not.toThrow();
        expect(hailingApiService.cancelHailingRequest).not.toHaveBeenCalled();
        expect(transactionEventService.addEvent).not.toHaveBeenCalled();
      });
    });
  });
});
