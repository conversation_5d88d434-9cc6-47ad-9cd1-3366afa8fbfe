/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpService } from "@nestjs/axios";
import { CallHandler, ExecutionContext, Inject, Injectable, NestInterceptor } from "@nestjs/common";
import { Request, Response } from "express";
import { omit } from "lodash";
import { Observable, throwError } from "rxjs";
import { tap, catchError } from "rxjs/operators";

import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";

import LoggerServiceAdapter from "../../modules/utils/logger/logger.service";

/**
 * Logger middleware
 */
@Injectable()
export class LoggerInterceptor implements NestInterceptor {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(ClsContextStorageService) private contextStorageService: ClsContextStorageService,
    private httpService: HttpService,
  ) {}

  intercept(context: ExecutionContext, next: <PERSON>Hand<PERSON>): Observable<any> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const { method, url, headers, body, query, originalUrl } = request;
    const ipData = headers["x-forwarded-for"] || request.socket.remoteAddress || "";
    const ip = Array.isArray(ipData) ? ipData[0] : ipData;

    const correlationId = this.logger.getContextId();
    if (request?.user?.uid) {
      this.contextStorageService.setAppUserId(request?.user?.uid);
    }

    if (request?.headers?.authorization) {
      this.contextStorageService.setToken(request?.headers?.authorization);
    }

    // Log incoming request
    this.logger.info(`[${method}] ${url}`, {
      // must keep this for tiny-url in logs, when there is "[GET] /links" in log, there will be a pubsub message
      method,
      ip: (ip ?? "").split(",")[0], // must keep this for tiny-url
      user: request?.user,
      userAgent: headers["user-agent"], // keep this for tiny-url
      acceptLanguage: headers["accept-language"], // must keep this for tiny-url
      headers: omit(headers, ["authorization", "cookie", "set-cookie"]),
      body,
      query,
      url,
      originalUrl, // must keep this for tiny-url !
    });

    this.httpService.axiosRef.interceptors.request.use((config) => {
      config.headers["X-Correlation-ID"] = correlationId;
      return config;
    });

    // Add header to response
    response.setHeader("X-Correlation-ID", correlationId);

    // Make custom header visible to clients through CORS
    response.setHeader("Access-Control-Expose-Headers", "X-Correlation-ID");

    return next.handle().pipe(
      tap((data) => {
        const executionDuration = Date.now() - startTime;
        const responseSize = JSON.stringify(data)?.length || 0;

        // Log successful response
        this.logger.info(`Response - [${method}] ${url}`, {
          method,
          url,
          body,
          query,
          originalUrl,
          statusCode: response.statusCode,
          response: data,
          executionDuration,
          responseSize,
          user: request?.user,
          ip,
          correlationId,
        });
      }),
      catchError((error) => {
        const executionDuration = Date.now() - startTime;

        // Log error response
        this.logger.error(`API Error - [${method}] ${url}`, {
          method,
          url,
          originalUrl,
          statusCode: error?.status || error?.statusCode || response.statusCode || 500,
          executionDuration,
          user: request?.user,
          ip,
          correlationId,
        }, error as Error);

        // Re-throw the error to ensure it's handled by the global exception filter
        return throwError(() => error);
      }),
    );
  }
}
