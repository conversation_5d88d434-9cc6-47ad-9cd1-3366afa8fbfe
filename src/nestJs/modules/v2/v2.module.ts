import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";

import { AdminAuthMiddleware } from "@nest/infrastructure/middlewares/adminAuth.middleware";
import { UserAppAuthMiddleware } from "@nest/infrastructure/middlewares/userAppAuth.middleware";

import { AdminOrganizationModule } from "./admin-organization/admin-organization.module";
import { HailingV2Module } from "./hailing/hailing-v2.module";
import { MeTxV2Module } from "./meTx/me-tx-v2.module";
import { PublicHailingV2Module } from "./public-hailing/public-hailing-v2.module";
import { B2bUsersModule } from "./b2b-users/b2b-users.module";
import { OrganizationModule } from "./organization/organization.module";
import { OrganizationTxModule } from "./organization-tx";
import { PaymentSessionModule } from "./payment-session/payment-session.module";

@Module({
  imports: [
    AdminOrganizationModule,
    HailingV2Module,
    MeTxV2Module,
    PublicHailingV2Module,
    B2bUsersModule,
    OrganizationModule,
    OrganizationTxModule,
    PaymentSessionModule,
  ],
})
export class V2Module implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminAuthMiddleware).forRoutes("v2/admin");
    consumer.apply(UserAppAuthMiddleware).forRoutes("/v2/me");
    consumer.apply(UserAppAuthMiddleware).forRoutes("/v2/organizations");
  }
}
