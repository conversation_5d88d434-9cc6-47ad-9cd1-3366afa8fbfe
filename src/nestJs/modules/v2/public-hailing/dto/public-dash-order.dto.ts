import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { HailUpdateResponse } from "@nest/modules/cloudTaskFleetOrder/interface";
import Tx from "@nest/modules/database/entities/tx.entity";
import { HailingItineraryStepResponse } from "@nest/modules/hailing/dto/hailing.dto";
import { isTxHailing } from "@nest/modules/transaction/dto/tx.dto";
import { TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { PaymentInformationStatus } from "@nest/modules/payment/dto/paymentInformationStatus.dto";
import { Heartbeat } from "@nest/modules/transaction/dto/txEventType.dto";

export class PublicDashOrder {
  @ApiProperty({
    description: "Unique identifier for the order",
    example: "12345",
  })
  id: string;

  @ApiProperty({
    description: "Order creation timestamp",
    example: "2023-08-14T10:30:00Z",
  })
  createdAt: Date;

  @ApiProperty({
    description: "Order last update timestamp",
    example: "2023-08-14T11:00:00Z",
  })
  updatedAt: Date;

  @ApiProperty({
    description: "Current status of the hailing request",
    enum: TxHailingRequestStatus,
    example: TxHailingRequestStatus.PENDING,
  })
  status: TxHailingRequestStatus;

  @ApiProperty({
    description: "Scheduled time for the trip",
    example: "14:30",
  })
  time: string;

  @ApiProperty({
    description: "Trip itinerary with steps and location details",
    isArray: true,
  })
  tripItinerary: HailingItineraryStepResponse[];

  @ApiPropertyOptional({
    description: "Encoded polyline for the trip itinerary",
    example: "encoded_polyline",
  })
  encodedPolyline?: string;

  @ApiProperty({
    description: "Total amount for the order",
    example: 100,
  })
  total: number;

  @ApiPropertyOptional({
    description: "Estimated time of arrival (ETA) in seconds",
    example: 300,
  })
  eta?: number;

  @ApiPropertyOptional({
    description: "Payment information for the order",
    type: "object",
    properties: {
      cardNumber: {
        type: "string",
        description: "Masked card number",
        example: "****1234",
      },
      paymentMethod: {
        type: "string",
        description: "Payment method used",
        example: "VISA",
      },
    },
  })
  paymentInformation?: {
    cardNumber?: string;
    paymentMethod?: string;
  };

  @ApiPropertyOptional({
    description: "Driver information when matched",
    type: "object",
    properties: {
      name: {
        type: "string",
        description: "Driver's name",
        example: "John Doe",
      },
      nameZh: {
        type: "string",
        description: "Driver's name in Chinese",
        example: "张三",
      },
      phoneNumber: {
        type: "string",
        description: "Driver's phone number",
        example: "+**********",
      },
      licensePlate: {
        type: "string",
        description: "Vehicle license plate",
        example: "ABC123",
      },
      heartBeat: {
        type: "object",
        properties: {
          location: {
            type: "object",
            properties: {
              lat: {
                type: "number",
                description: "Latitude",
                example: 123.456,
              },
              lng: {
                type: "number",
                description: "Longitude",
                example: 789.012,
              },
            },
          },
          bearing: {
            type: "number",
            description: "Bearing",
            example: 45,
          },
          speed: {
            type: "number",
            description: "Speed",
            example: 10,
          },
        },
      },
      vehicle: {
        type: "object",
        properties: {
          model: {
            type: "string",
            description: "Vehicle model",
            example: "Toyota Camry",
          },
          iconUrl: {
            type: "string",
            description: "URL to vehicle icon",
            example: "https://example.com/car-icon.png",
          },
        },
      },
    },
  })
  driver?: {
    name: string;
    nameZh: string;
    phoneNumber: string;
    licensePlate: string;
    vehicle: {
      model: string;
      iconUrl?: string;
    };
    heartBeat?: Heartbeat;
  };

  @ApiPropertyOptional({
    description: "User information",
    type: "object",
    properties: {
      id: {
        type: "string",
        description: "User id",
        example: "12345",
      },
      phoneNumber: {
        type: "string",
        description: "User phone number",
        example: "+**********",
      },
    },
  })
  user?: {
    id: string;
    phoneNumber: string;
  };

  constructor(tx: Tx, hail?: HailUpdateResponse) {
    if (isTxHailing(tx)) {
      this.id = tx.id;
      this.createdAt = tx.createdAt;
      this.updatedAt = tx.updatedAt;
      this.status = tx.metadata.status;
      this.time = tx.metadata.time;
      this.tripItinerary = tx.metadata.itinerary;
      this.encodedPolyline = tx.metadata.tripEstimation?.encodedPolyline;
      this.total = tx.metadata.minMaxFareCalculations?.[0].total ?? 0;
      const paymentTx = tx.paymentTx?.find((paymentTx) => paymentTx.status === PaymentInformationStatus.SUCCESS);

      if (paymentTx) {
        this.paymentInformation = {
          cardNumber: paymentTx.cardNumber,
          paymentMethod: paymentTx.paymentMethod,
        };
      }

      if (tx.user) {
        this.user = {
          id: tx.user.id,
          phoneNumber: tx.user.phoneNumber,
        };
      }

      if (hail) {
        this.driver = {
          name: hail.matchedDriver?.name ?? "",
          nameZh: hail.matchedDriver?.nameTc ?? "",
          phoneNumber: hail.matchedDriver?.phoneNumber ?? "",
          licensePlate: hail.matchedDriver?.licensePlate ?? "",
          vehicle: {
            model: hail.matchedDriver?.vehicleModel ?? "",
            iconUrl: hail.matchedDriver?.vehicleIconUrl,
          },
          heartBeat: hail.heartBeat,
        };
        this.eta = hail.eta;
      }
    }
  }

  toJSON() {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      status: this.status,
      time: this.time,
      tripItinerary: this.tripItinerary,
      paymentInformation: this.paymentInformation,
      driver: this.driver,
      total: this.total,
      eta: this.eta,
      encodedPolyline: this.encodedPolyline,
      user: this.user,
    };
  }
}
