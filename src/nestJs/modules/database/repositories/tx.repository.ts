import { Inject, Injectable } from "@nestjs/common";
import { InjectEntityManager, InjectRepository } from "@nestjs/typeorm";
import { DecodedIdToken } from "firebase-admin/auth";
import { Brackets, EntityManager, In, <PERSON>N<PERSON>, Not, SelectQueryBuilder } from "typeorm";
import { Repository } from "typeorm/repository/Repository";

import { isTxHailing } from "@nest/modules/transaction/dto/tx.dto";
import { TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import { TxTagType } from "@nest/modules/transaction/dto/txTagType.dto";
import { ClientType } from "@nest/modules/v2/hailing/dto/create-hail.dto";
import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";

import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { MerchantInfo } from "../../transaction/dto/createPayout.dto";
import { TxEventType } from "../../transaction/dto/txEventType.dto";
import { TxPayoutStatus } from "../../transaction/dto/txPayoutStatus.dto";
import { TxTypes } from "../../transaction/dto/txType.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { roundOneDecimal } from "../../utils/utils/number.utils";
import { UtilsService } from "../../utils/utils.service";
import { MerchantForPayout } from "../entities/merchant.entity";
import PaymentTx from "../entities/paymentTx.entity";
import Tx from "../entities/tx.entity";
import User from "../entities/user.entity";

import { PaymentTxRepository } from "./paymentTx.repository";

@Injectable()
export class TxRepository extends Repository<Tx> {
  constructor(
    @InjectRepository(Tx) txRepository: Repository<Tx>,
    @InjectEntityManager() private entityManager: EntityManager,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly clsContextStorageService: ClsContextStorageService,
  ) {
    super(txRepository.target, txRepository.manager, txRepository.queryRunner);
  }

  async upsertTxAndUpdatePaymentTx(
    tx: Tx,
    paymentTxs: PaymentTx[],
    shouldUpdateTxMetadata: (currentTx: Tx, newTx: Tx) => { shouldUpdate: boolean; condition: string },
    shouldQueryWithAllRelations: (tx: Tx) => boolean,
  ): Promise<Tx> {
    const foundTx = await this.findOne({
      where: { id: tx.id },
      relations: shouldQueryWithAllRelations(tx) ? ["txTag", "merchant", "txApp"] : undefined,
    });

    if (!foundTx) {
      tx.paymentTx = paymentTxs;
      await this.upsert(tx, { conflictPaths: ["id"], skipUpdateIfNoValuesChanged: true });
      return tx;
    }

    const newPaymentTx: PaymentTx[] = [];
    let foundPaymentTx = paymentTxs;
    // paymentTxs && paymentTxs.length > 0 will cover the case where there is no payment_information in firestore
    // shouldQueryWithAllRelations will cover street trip with user app payment, which does not have payment information in firestore
    if ((paymentTxs && paymentTxs.length > 0) || shouldQueryWithAllRelations(tx)) {
      this.logger.debug(`upsertTxAndUpdatePaymentTx: found paymentTxs for tx ${tx.id}`);
      foundPaymentTx = await this.paymentTxRepository.find({
        relations: ["paymentInstrument"],
        where: { tx: { id: tx.id } },
        loadEagerRelations: false,
      });
      paymentTxs.forEach((ptx) => {
        if (
          !foundPaymentTx.find((p) => p.id === ptx.id) ||
          foundPaymentTx.find((p) => p.id === ptx.id && p.createdAt.getTime() !== ptx.createdAt.getTime())
        ) {
          foundPaymentTx.push(ptx);
          newPaymentTx.push(ptx);
        }
      });
    }

    const { shouldUpdate, condition } = shouldUpdateTxMetadata(foundTx, tx);
    if (shouldUpdate) {
      foundTx.metadata = tx.metadata;
      foundTx.total = tx.total;
      foundTx.dashFee = tx.dashFee;
      foundTx.payoutAmount = tx.payoutAmount;
      foundTx.merchant = tx.merchant;
      foundTx.payoutMerchant = tx.payoutMerchant;
      foundTx.discount = tx.discount;
      foundTx.bonus = tx.bonus;
      if (tx.user?.id) {
        const user = new User();
        user.id = tx.user.id;
        foundTx.user = user;
      }
    }
    foundTx.paymentTx = foundPaymentTx;
    const result = await this.createQueryBuilder()
      .update(Tx)
      .set({
        metadata: foundTx.metadata,
        total: foundTx.total,
        dashFee: foundTx.dashFee,
        payoutAmount: foundTx.payoutAmount,
        payoutMerchant: foundTx.payoutMerchant,
        discount: foundTx.discount,
        bonus: foundTx.bonus,
        user: foundTx.user,
      })
      .where("id = :id", { id: tx.id })
      .andWhere(condition)
      .execute();
    if (result.affected === 0) {
      this.logger.debug(`Tx not updated: ${tx.id}`);
    } else {
      this.logger.debug(`Tx updated: ${tx.id}`);
    }
    if (newPaymentTx.length > 0) {
      await this.paymentTxRepository.upsertPaymentTx(newPaymentTx);
    }
    this.logger.debug("upsertTxAndUpdatePaymentTx, finally foundTx", { foundTx: foundTx, paymentTxs: foundPaymentTx });
    return foundTx;
  }

  async createTxAdjustment(
    parentTxId: string,
    total: number,
    reason: string,
    user: DecodedIdToken,
  ): Promise<{ adjustmentTx: Tx; parentTx: Tx }> {
    const parentTx = await this.entityManager.findOne(Tx, {
      relations: ["merchant", "payoutMerchant", "user"],
      where: { id: parentTxId },
    });

    if (!parentTx) {
      throw errorBuilder.transaction.notFound(parentTxId);
    }

    const adjustmentTx = Tx.newAdjustmentFromJson({
      parentTx,
      total: this.utilsService.number.roundOneDecimal(total),
      reason,
      createdBy: user.email,
    });

    parentTx.adjustment = (parentTx.adjustment ?? 0) + total;

    return { adjustmentTx, parentTx };
  }

  /**
   * Update tx payout status and get drivers data for payout
   * TODO: There is no lock here so if two requests come in at the same time, it will cause issues
   * @param txIds string[]
   * @returns { merchants: MerchantInfo[]; processed: string[]; unprocessed: string[] }
   */
  async updatePayoutAndGetMerchantsData(txIds: string[]): Promise<{
    merchants: MerchantInfo[];
    processed: string[];
    unprocessed: string[];
    reasons: string[];
  }> {
    this.logger.debug(`updatePayoutAndGetMerchantsData: ${txIds}`);
    return this.entityManager.transaction(async (entityManager) => {
      const txs = await entityManager.find(Tx, {
        relations: ["merchant", "payoutMerchant", "txTag"],
        where: { id: In(txIds), payoutStatus: IsNull() },
      });

      const result = txIds.reduce(
        (
          acc: {
            unprocessed: string[];
            merchants: Record<string, MerchantInfo>;
            reasons: string[];
          },
          id: string,
        ) => {
          const found = txs.find((r) => r.id === id);
          let merchant: MerchantForPayout | undefined;

          if (found && !found?.txTag?.length && found.payoutMerchant && found.payoutMerchant.canReceivePayout()) {
            merchant = found.payoutMerchant;
          } else if (found && !found?.txTag?.length && found.merchant && found.merchant.canReceivePayout()) {
            merchant = found.merchant;
          } else {
            acc.unprocessed.push(id);
            if (!found) {
              acc.reasons.push(`Transaction ${id} not found, check if it exists and that payoutStatus is Null`);
            }
            if (found?.type === TxTypes.TX_ADJUSTMENT) {
              this.logger.debug(`Transaction ${id} is an adjustment`, {
                txTag: found.txTag,
                merchant: found.merchant,
              });
            }
            if (found?.txTag?.length) {
              acc.reasons.push(
                `Transaction ${id} has tags, resolve them first: ${found?.txTag.map((t) => t.tag).join(", ")}`,
              );
            }
            if (!found?.merchant) {
              acc.reasons.push(`Transaction ${id} does not have a merchant`);
            }
            if (!found?.merchant?.name) {
              acc.reasons.push(`Transaction ${id} does not have a merchant name`);
            }
            if (!found?.merchant?.bankAccount) {
              acc.reasons.push(`Transaction ${id} does not have a merchant bank account`);
            }
            if (!found?.merchant?.bankAccountOwnerName) {
              acc.reasons.push(`Transaction ${id} does not have a merchant bank account owner name`);
            }
            if (!found?.merchant?.bankId) {
              acc.reasons.push(`Transaction ${id} does not have a merchant bank id`);
            }

            if (found?.payoutMerchant) {
              if (!found?.payoutMerchant?.name) {
                acc.reasons.push(`Transaction ${id} does not have a payout merchant name`);
              }
              if (!found?.payoutMerchant?.bankAccount) {
                acc.reasons.push(`Transaction ${id} does not have a payout merchant bank account`);
              }
              if (!found?.payoutMerchant?.bankAccountOwnerName) {
                acc.reasons.push(`Transaction ${id} does not have a payout merchant bank account owner name`);
              }
              if (!found?.payoutMerchant?.bankId) {
                acc.reasons.push(`Transaction ${id} does not have a payout merchant bank id`);
              }
            }
          }

          if (found && merchant) {
            if (!acc.merchants[merchant.id]) {
              acc.merchants[merchant.id] = {
                id: merchant.id,
                name: merchant.name,
                phoneNumber: merchant.phoneNumber,
                bankAccount: merchant.bankAccount,
                bankAccountOwnerName: merchant.bankAccountOwnerName,
                bankId: merchant.bankId,
                txIds: [],
                total: 0,
              };
            }
            if (found.type === TxTypes.TX_ADJUSTMENT) {
              acc.merchants[merchant.id].total = roundOneDecimal(
                (found.payoutAmount ?? 0) + acc.merchants[merchant.id].total,
              );
            } else {
              acc.merchants[merchant.id].total = roundOneDecimal(
                (found.payoutAmount ?? 0) + acc.merchants[merchant.id].total,
              );
            }
            acc.merchants[merchant.id].txIds.push(id);
          }

          return acc;
        },
        { unprocessed: [], merchants: {}, reasons: [] },
      );

      let { unprocessed } = result;
      const { merchants, reasons } = result;

      Object.entries(merchants).forEach(([merchantId, merchant]) => {
        if (merchant.total <= 0) {
          reasons.push(
            `Merchant ${merchantId} has a balance of ${
              merchant.total
            }, the following tx won't be processed: ${merchant.txIds.join(", ")}`,
          );
          unprocessed = unprocessed.concat(merchant.txIds);
          delete merchants[merchantId];
        }
      });

      const processed = txIds.filter((id) => !unprocessed.includes(id));

      await entityManager.update(
        Tx,
        { id: In(processed), payoutStatus: IsNull() },
        { payoutStatus: TxPayoutStatus.PRERELEASED },
      );

      return { processed, unprocessed, merchants: Object.values(merchants), reasons };
    });
  }

  /**
   * get tx list by userId
   * @param userId string
   * @param take number
   * @param skip number
   * @returns Promise<Tx[], number>
   */
  async getCompletedTxListByUserId(userId: string, take: number = 10, skip: number = 0): Promise<[Tx[], number]> {
    return this.createQueryBuilder("tx")
      .leftJoinAndSelect("tx.paymentTx", "paymentTx")
      .leftJoinAndSelect("tx.txEvents", "txEvents")
      .where("tx.user = :user", { user: userId })
      .andWhere(
        new Brackets((qb) => {
          qb.where("tx.metadata ->> 'clientType' = :clientType", { clientType: ClientType.DASH }).orWhere(
            "tx.metadata ->> 'clientType' IS NULL",
          );
        }),
      )
      .andWhere(
        new Brackets((qb) => {
          qb.where(
            // List the trips that have been completed and the hailing requests that have been cancelled late by the user
            new Brackets((qb) => {
              qb.where("tx.type IN (:...txTypes)", { txTypes: [TxTypes.TRIP, TxTypes.HAILING_REQUEST] }).andWhere(
                (qb: SelectQueryBuilder<Tx>) => {
                  const subQueryAlias = qb
                    .subQuery()
                    .select("paymentTx.id")
                    .from("payment_tx", "paymentTx")
                    .where("paymentTx.status = :statusSub1", { statusSub1: PaymentInformationStatus.SUCCESS })
                    .andWhere("paymentTx.type IN (:...paymentTxTypes)", {
                      paymentTxTypes: [PaymentInformationType.CAPTURE, PaymentInformationType.SALE],
                    })
                    .getQuery();
                  return `paymentTx.id = ANY(${subQueryAlias})`;
                },
              );
            }),
          )
            .orWhere(
              // List the hailing requests that have been cancelled by the user or have timed out
              new Brackets((qb) => {
                qb.where("tx.type = :type", { type: TxTypes.HAILING_REQUEST }).andWhere(
                  (qb: SelectQueryBuilder<Tx>) => {
                    const subQueryAlias = qb
                      .subQuery()
                      .select("txEvents.id")
                      .from("tx_event", "txEvents")
                      .andWhere("txEvents.type IN (:...txEventTypes)", {
                        txEventTypes: [
                          TxEventType.HAILING_USER_CANCELS_ORDER,
                          TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT,
                        ],
                      })
                      .getQuery();
                    return `txEvents.id = ANY(${subQueryAlias})`;
                  },
                );
              }),
            )
            .orWhere(
              // List the trips that have NO_PAYMENT_NEEDED tag
              new Brackets((qb) => {
                qb.where("tx.type = :type", { type: TxTypes.TRIP }).andWhere((qb: SelectQueryBuilder<Tx>) => {
                  const subQuery = qb
                    .subQuery()
                    .select("txTag.id")
                    .from("tx_tag", "txTag")
                    .where("txTag.txId = tx.id")
                    .andWhere("txTag.tag = :noPaymentNeededTag", { noPaymentNeededTag: TxTagType.NO_PAYMENT_NEEDED })
                    .withDeleted()
                    .getQuery();
                  return `EXISTS(${subQuery})`;
                });
              }),
            );
        }),
      )
      .take(take)
      .skip(skip)
      .orderBy("tx.createdAt", "DESC")
      .getManyAndCount();
  }

  async updateHailingTxToCancelled(txId: string): Promise<Tx> {
    const tx = await this.findOne({ where: { id: txId } });

    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    if (isTxHailing(tx)) {
      tx.metadata.status = TxHailingRequestStatus.CANCELLED;
      tx.metadata.cancelledAt = new Date();
    }

    return this.save(tx);
  }

  async findTxByStatusAndUserId(userId: string, limit: number = 50) {
    const status = [
      TxHailingRequestStatus.COMPLETED,
      TxHailingRequestStatus.CANCELLED,
      TxHailingRequestStatus.TIMED_OUT,
    ];
    const txs = await this.createQueryBuilder("tx")
      .leftJoin("tx.merchant", "merchant")
      .addSelect(["merchant.id", "merchant.phoneNumber", "merchant.name", "merchant.nameLocal"])
      .andWhere("tx.userId = :userId", { userId })
      .andWhere("tx.type = :type", { type: TxTypes.HAILING_REQUEST })
      .andWhere("tx.metadata ->>'clientType' = :clientType", { clientType: ClientType.B2B_APP })
      .andWhere("tx.metadata ->>'status' NOT IN (:...status)", {
        status,
      })
      .orderBy("tx.createdAt", "DESC")
      .take(limit)
      .getMany();

    // For each transaction, get payment data from parent if exists, otherwise from current tx
    const txsWithPayment = await Promise.all(
      txs.map(async (tx) => {
        const paymentTxId = tx.parentTxId || tx.id;
        const paymentTx = await this.paymentTxRepository.find({
          where: { tx: { id: paymentTxId } },
          loadEagerRelations: false,
        });
        return { ...tx, paymentTx };
      })
    );

    const total = await this.count({
      where: {
        userId,
        type: TxTypes.HAILING_REQUEST,
        metadata: {
          status: Not(In(status)),
          clientType: ClientType.B2B_APP,
        },
      },
    });

    return {
      items: txsWithPayment as Tx[],
      total,
      limit,
    };
  }

  async findHistoricHailingTxByStatusAndUserId(userId: string, page: number = 1, limit: number = 50) {
    const txs = await this.createQueryBuilder("tx")
      .leftJoin("tx.paymentTx", "paymentTx")
      .addSelect([
        "paymentTx.id",
        "paymentTx.status",
        "paymentTx.type",
        "paymentTx.cardNumber",
        "paymentTx.amount",
        "paymentTx.createdAt",
        "paymentTx.updatedAt",
      ])
      .leftJoin("tx.merchant", "merchant")
      .addSelect(["merchant.id", "merchant.phoneNumber", "merchant.name", "merchant.nameLocal"])
      .andWhere("tx.userId = :userId", { userId })
      .andWhere("tx.metadata ->> 'clientType' = :clientType", { clientType: ClientType.B2B_APP })
      .andWhere(
        new Brackets((qb) => {
          qb.where(
            new Brackets((qb) => {
              qb.where("tx.type = :tripType", { tripType: TxTypes.TRIP })
                .andWhere("tx.metadata ->> 'tripEnd' IS NOT NULL");
            })
          ).orWhere(
            new Brackets((qb) => {
              qb.where("tx.type = :hailingType", { hailingType: TxTypes.HAILING_REQUEST })
                .andWhere("tx.metadata ->> 'status' IN (:...cancelledStatuses)", {
                  cancelledStatuses: [TxHailingRequestStatus.CANCELLED, TxHailingRequestStatus.TIMED_OUT]
                });
            })
          );
        })
      )
      .orderBy("tx.createdAt", "DESC")
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    const total = await this.count({
      where: [
        {
          userId,
          type: TxTypes.TRIP,
          metadata: {
            clientType: ClientType.B2B_APP,
            tripEnd: Not(IsNull()),
          },
        },
        {
          userId,
          type: TxTypes.HAILING_REQUEST,
          metadata: {
            clientType: ClientType.B2B_APP,
            status: In([TxHailingRequestStatus.CANCELLED, TxHailingRequestStatus.TIMED_OUT]),
          },
        },
      ],
    });

    return {
      items: txs,
      total,
      limit,
    };
  }

  async findHailingRequestByTxIdForPublicView(txId: string): Promise<Tx | null> {
    // Force query to use master database by creating a query runner with master connection
    const entityManager = this.clsContextStorageService.getMasterEntityManager() || this.entityManager;
    const queryRunner = entityManager.connection.createQueryRunner("master");
    
    try {
      const tx = await queryRunner.manager.createQueryBuilder(Tx, "tx")
        .select([
          "tx.id",
          "tx.userId",
          "tx.parentTxId",
          "tx.type",
          "tx.metadata",
          "tx.createdAt",
          "tx.updatedAt",
          "tx.total",
          "tx.dashFee",
          "tx.discount",
        ])
        .leftJoin("tx.merchant", "merchant")
        .addSelect(["merchant.id", "merchant.phoneNumber", "merchant.name", "merchant.nameLocal"])
        .leftJoin("tx.user", "user")
        .addSelect(["user.id", "user.phoneNumber"])
        .where("tx.id = :txId", { txId })
        .getOne();

      if (!tx) {
        return null;
      }

      const paymentTx = await queryRunner.manager.find(PaymentTx, {
        where: { tx: { id: In([tx.id, tx.parentTxId]) } },
        loadEagerRelations: false,
      });

      return { ...tx, paymentTx } as Tx;
    } finally {
      await queryRunner.release();
    }
  }
}
