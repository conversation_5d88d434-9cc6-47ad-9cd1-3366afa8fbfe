import { Injectable } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";
import { ConfigService } from "@nestjs/config";

import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { SubTxTypes, TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { AppUser } from "@nest/modules/user/dto/user.dto";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { PaymentService } from "@nest/modules/payment/payment.service";
import { PaymentInformationType } from "@nest/modules/payment/dto/paymentInformationType.dto";
import Tx from "@nest/modules/database/entities/tx.entity";
import { PaymentInformationStatus } from "@nest/modules/payment/dto/paymentInformationStatus.dto";
import { LocalizedLanguage, LocationLanguage, TripEstimation } from "@nest/modules/location/dto/location.dto";
import { TxAppRepository } from "@nest/modules/database/repositories/app.repository";
import { LocationService } from "@nest/modules/location/location.service";
import { TxAppsNames } from "@nest/modules/apps/dto/Apps.dto";
import PaymentInstrument from "@nest/modules/database/entities/paymentInstrument.entity";
import { HailingItineraryStepResponse } from "@nest/modules/hailing/dto/hailing.dto";
import { CampaignService } from "@nest/modules/campaign/campaign.service";
import { PubSubService } from "@nest/modules/pubsub/pubsub.service";
import { TxHailingMetadata } from "@nest/modules/transaction/dto/txMetadata.dto";
import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";
import { HailingApiCreateOrderResponse } from "@nest/modules/hailing/dto/hailing.api.dto";
import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";
import { RuleParams } from "@nest/modules/jsonLogic/dto/ruleParams.dto";
import { PaymentChannelType } from "@nest/modules/campaign/dto/campaign.dto";
import dayjs from "@nest/modules/utils/dayjs";
import { CreateFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/CreateFleetOrderDelegatee";
import { FleetVehicleType } from "@nest/modules/appDatabase/documents/fleetVehicleType.document";
import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { ChannelTypes } from "@nest/modules/message/dto/channelType.dto";
import { TemplateTypesText } from "@nest/modules/message/dto/templateType.dto";
import { UtilsService } from "@nest/modules/utils/utils.service";
import { LogAll } from "@nest/decorators/log-all.decorator";
import { HailingCreateOrderBody } from "@nest/modules/me/modules/meHailing/dto/meHailing.dto";
import { HailType } from "@nest/modules/transaction/dto/txHailingRequest.dto";

import { HailingV2Service } from "../services/hailing-v2.service";
import { ClientType, FareCalculation, HailingCreateOrderBodyV3, PlatformType } from "../dto/create-hail.dto";
import { HailingDiscount } from "../dto/discount.dto";
import { PriceRuleService } from "../../priceRule/services/price-rule.service";

@LogAll()
@Injectable()
export class CreateHailingRequestProcessor {
  constructor(
    private readonly hailingApiService: HailingApiService,
    private readonly userRepository: UserRepository,
    private readonly txRepository: TxRepository,
    private readonly paymentService: PaymentService,
    private readonly txAppRepository: TxAppRepository,
    private readonly locationService: LocationService,
    private readonly campaignService: CampaignService,
    private readonly pubsubService: PubSubService,
    private readonly clsService: ClsContextStorageService,
    private readonly hailingV2Service: HailingV2Service,
    private readonly createFleetOrderDelegatee: CreateFleetOrderDelegatee,
    private readonly appDatabaseService: AppDatabaseService,
    private readonly utilsService: UtilsService,
    private readonly configService: ConfigService,
    private readonly priceRuleService: PriceRuleService,
  ) {}

  async execute(body: HailingCreateOrderBodyV3, user: DecodedIdToken, token: string): Promise<Tx> {
    try {
      this.clsService.setToken(token);

      body.time = body.time ?? new Date();

      const appUser = await this.getAppUser(user);
      const tripItinerary = await this.getTripItinerary(body);

      const validPaymentInstrument = await this.hailingV2Service.getValidPaymentInstrument(
        appUser,
        body.paymentInstrumentId,
      );

      if (!validPaymentInstrument && body.clientType === ClientType.DASH) {
        throw errorBuilder.payment.instrument.notFound(body.paymentInstrumentId ?? "preferred");
      }

      const discount = await this.getDiscounts(body, appUser, validPaymentInstrument);

      const tripEstimation = await this.locationService.computeRoutes({
        originPlaceId: body.itinerary[0].placeId,
        destinationPlaceId: body.itinerary[body.itinerary.length - 1].placeId,
        language: body.language === LocalizedLanguage.EN ? LocationLanguage.EN : LocationLanguage.ZH_HK,
      });

      const tx = await this.beforeCreateHailingRequest(body, appUser, tripItinerary, tripEstimation);

      const priceRules = await this.priceRuleService.getActivePricingRules(
        tx.metadata.platformType,
        tx.metadata.clientType as ClientType,
        tx.metadata.type as HailType,
      );

      const {
        max: amountToAuth,
        minFareCalculation,
        maxFareCalculation,
      } = await this.hailingApiService.getPriceEstimationV3(body, tripItinerary, discount, priceRules);

      const afterPriceTx = await this.afterPriceEstimation(
        tx,
        body,
        amountToAuth,
        [minFareCalculation, maxFareCalculation],
        discount,
      );

      this.clsService.setCreateHailingRequestTxId(tx.id);

      if (body.clientType === ClientType.B2B_APP) {
        return afterPriceTx;
      }

      await this.applyDiscounts(body, tx, user);

      const paymentTx = await this.processPreAuth(tx, amountToAuth, validPaymentInstrument);

      const fleetVehicle = await this.getFleetVehicle(body);

      const hailingResponse = await this.hailingApiService.createHailingOrderV3(
        body,
        tx,
        discount,
        tripEstimation,
        tripItinerary,
        priceRules,
        fleetVehicle,
        paymentTx,
      );

      const updatedTx = await this.afterCreateHailingRequest(tx, hailingResponse, body, discount, paymentTx);

      if (body.platformType === PlatformType.FLEET && body.clientType === ClientType.DASH) {
        await this.createFleetOrderDelegatee.execute(
          {
            ...body,
            itinerary: tripItinerary,
          } as unknown as HailingCreateOrderBody,
          tx,
          user,
        );
      }

      await this.pubsubService.publishMessageForHailingTxCreated({ txId: tx.id });

      await this.afterAll(appUser, tx, body, amountToAuth);

      return updatedTx;
    } catch (error) {
      const txId = this.clsService.getCreateHailingRequestTxId();
      if (txId) {
        await this.hailingV2Service.updateTxToFailed(txId);
      }
      throw error;
    }
  }

  async getFleetVehicle(body: HailingCreateOrderBodyV3): Promise<FleetVehicleType | undefined> {
    if (!body.fleetVehicleType) {
      return undefined;
    }

    const fleetVehicle = await this.appDatabaseService
      .fleetVehicleTypesRepository()
      .getFleetVehicleType(body.fleetVehicleType);

    if (!fleetVehicle) {
      throw errorBuilder.fleetTaxi.noFleetVehicleTypeFound(body.fleetVehicleType);
    }

    return fleetVehicle;
  }

  async beforeCreateHailingRequest(
    body: HailingCreateOrderBodyV3,
    appUser: AppUser,
    tripItinerary: HailingItineraryStepResponse[],
    tripEstimation: TripEstimation,
  ) {
    const txApp = await this.txAppRepository.appByNameOrCreate(TxAppsNames.TAPXI);

    const tx = Tx.fromHailingRequest(body, appUser, txApp, tripItinerary, body.clientType, tripEstimation);

    await this.txRepository.save(tx);
    return tx;
  }

  async getAppUser(user: DecodedIdToken) {
    const appUser = await this.userRepository.findAppUserById(user.uid, ["paymentInstruments"]);

    return appUser;
  }

  async afterPriceEstimation(
    tx: Tx,
    body: HailingCreateOrderBodyV3,
    amountToAuth: number,
    minMaxFareCalculations: FareCalculation[],
    discounts: HailingDiscount,
  ) {
    tx.metadata = {
      ...tx.metadata,
      authedAmount: amountToAuth,
      minMaxFareCalculations,
      discounts,
      guest: body.metadata?.guest,
    } as TxHailingMetadata;
    tx.metadata.qrWebReceiptUrl = `${this.configService.get("DASH_WEB_PUBLIC_URL")}/web-receipt/${tx.id}`;
    await this.txRepository.save(tx);
    return tx;
  }

  async afterCreateHailingRequest(
    tx: Tx,
    hailingResponse: HailingApiCreateOrderResponse,
    body: HailingCreateOrderBodyV3,
    discounts: HailingDiscount,
    paymentTx?: PaymentTx,
  ): Promise<Tx> {
    tx.metadata = {
      ...hailingResponse,
      charges: {
        cancellationFee: 0,
      },
      discounts,
      request: body,
      guest: body.metadata?.guest,
    } as TxHailingMetadata;

    if (paymentTx) {
      tx.paymentTx = [paymentTx];
    }
    return this.txRepository.save(tx);
  }

  async afterAll(appUser: AppUser, tx: Tx, body: HailingCreateOrderBodyV3, amountToAuth: number) {
    const isUserMakingFirstHailingRequest = await this.isUserMakingFirstHailingRequest(appUser);

    if (isUserMakingFirstHailingRequest && appUser.phoneNumber) {
      await this.pubsubService.publishMessageForMessageProcessingParams({
        metadata: {
          schemeVersion: "1.0",
          createdAt: body.time ?? new Date(),
        },
        recipient: {
          phone: appUser.phoneNumber,
        },
        tranId: tx.id,
        channel: ChannelTypes.WHATSAPP,
        language: this.utilsService.language.getLanguageType(body.language),
        params: {
          amount: amountToAuth,
        },
        messageId: tx.id,
        template: TemplateTypesText.FIRST_HAIL_ORDER,
      });
    }

    return isUserMakingFirstHailingRequest;
  }

  async isUserMakingFirstHailingRequest(appUser: AppUser) {
    const isUserExistAnyHailingRequest = await this.txRepository.exists({
      where: { user: { id: appUser.id }, type: TxTypes.HAILING_REQUEST },
    });

    return !isUserExistAnyHailingRequest;
  }

  async processPreAuth(tx: Tx, amountToAuth: number, validPaymentInstrument?: PaymentInstrument) {
    if (!validPaymentInstrument) {
      return undefined;
    }

    const paymentTx = await this.paymentService.processAuth(tx, validPaymentInstrument, amountToAuth);

    paymentTx.requestedBy = "SYSTEM";

    if (
      !paymentTx ||
      paymentTx.type !== PaymentInformationType.AUTH ||
      paymentTx.status !== PaymentInformationStatus.SUCCESS
    ) {
      throw errorBuilder.payment.authFailed(paymentTx.gatewayResponse);
    }

    return paymentTx;
  }

  async getTripItinerary(body: HailingCreateOrderBodyV3) {
    // Convert body.language to LocationLanguage for proper comparison
    const bodyLang = body.language === LocalizedLanguage.EN ? LocationLanguage.EN : LocationLanguage.ZH_HK;
    const missingTranslationLanguage = [LocationLanguage.EN, LocationLanguage.ZH_HK].find((lang) => lang !== bodyLang);

    if (!missingTranslationLanguage) {
      throw errorBuilder.global.badRequest("Missing translation language");
    }

    const promises = await Promise.all(
      body.itinerary.map((itineraryStep) =>
        this.locationService.getPlaceDetails(
          {
            placeId: itineraryStep.placeId,
            language: missingTranslationLanguage,
          },
          body.sessionToken,
        ),
      ),
    );

    return body.itinerary.map<HailingItineraryStepResponse>((itineraryStep, index) => ({
      index: index,
      placeId: itineraryStep.placeId,
      lat: promises[index].lat ?? 0,
      lng: promises[index].lng ?? 0,
      i18n: {
        [missingTranslationLanguage]: {
          displayName: promises[index].displayName,
          formattedAddress: promises[index].formattedAddress,
        },
        [body.language]: {
          displayName: itineraryStep.displayName,
          formattedAddress: itineraryStep.formattedAddress,
        },
      },
    }));
  }

  async getDiscounts(
    body: HailingCreateOrderBodyV3,
    user: AppUser,
    paymentInstrument?: PaymentInstrument,
  ): Promise<HailingDiscount> {
    const ruleParams = new RuleParams({
      userId: user.id,
      transactionType: TxTypes.TRIP,
      transactionSubtype: SubTxTypes.HAILING,
      paymentInstrumentType: paymentInstrument?.cardType,
      paymentChannel: PaymentChannelType.APP,
      originPlaceId: body.itinerary[0].placeId,
      destinationPlaceId: body.itinerary[body.itinerary.length - 1].placeId,
      timeOfDay: dayjs(body.time).format("HH:mm"),
      dayOfWeek: dayjs(body.time).day() + 1,
    });

    const [thirdPartyCampaign, dashCampaign] = await this.campaignService.getApplicableCampaigns(ruleParams);

    return {
      discountIdThirdParty: thirdPartyCampaign?.id,
      discountRulesThirdParty: thirdPartyCampaign?.discountRules,
      discountIdDash: dashCampaign?.id,
      discountRulesDash: dashCampaign?.discountRules,
    };
  }

  async applyDiscounts(body: HailingCreateOrderBodyV3, tx: Tx, user: DecodedIdToken): Promise<HailingDiscount> {
    let discountIdThirdParty: string | undefined;
    let discountRulesThirdParty: string | undefined;
    let discountIdDash: string | undefined;
    let discountRulesDash: string | undefined;

    try {
      if (body.campaignIdThirdParty) {
        const discountThirdParty = await this.campaignService.applyCampaignByIdAndUserId(
          body.campaignIdThirdParty,
          tx,
          user.uid,
        );
        discountIdThirdParty = discountThirdParty.id;
        discountRulesThirdParty = discountThirdParty.campaign.discountRules;
      }

      if (body.campaignIdDash) {
        const discountDash = await this.campaignService.applyCampaignByIdAndUserId(body.campaignIdDash, tx, user.uid);
        discountIdDash = discountDash.id;
        discountRulesDash = discountDash.campaign.discountRules;
      }

      return { discountIdThirdParty, discountRulesThirdParty, discountIdDash, discountRulesDash };
    } catch (e) {
      return { discountIdThirdParty, discountRulesThirdParty, discountIdDash, discountRulesDash };
    }
  }
}
